package main

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"

	"gorm.io/gorm"
)

func main() {
	model.Init()
	err := model.DB.AutoMigrate(
		&model.User{}, &model.Workflow{}, &model.UserBalance{},
		&model.WorkflowLog{}, &model.Plugin{}, &model.LLMUsageReport{},
		&model.ManualLLM{}, &model.Order{}, &model.APIKey{}, &model.PublishedWorkflow{},
		&model.VerifyCode{},
	)
	if err != nil {
		panic(err)
	}

	// 检查是否需要更新 UUID
	if utils.GetEnvWithDefault("UPDATE_EMPTY_UUIDS", "false") == "true" {
		if err := updateEmptyUUIDs(model.DB); err != nil {
			panic(err)
		}
	}

	// 创建触发器
	if err := balanceTrigger(model.DB); err != nil {
		panic(err)
	}
}

func balanceTrigger(tx *gorm.DB) error {
	// 检查并创建触发器函数
	// 检查并创建触发器函数
	createFunctionSQL := `
    CREATE OR REPLACE FUNCTION deduct_or_create_user_credits()
    RETURNS TRIGGER AS $$
    BEGIN
        -- 检查用户是否存在
        IF NOT EXISTS (SELECT 1 FROM user_balances WHERE user_id = NEW.uid) THEN
            -- 如果用户不存在，则先创建用户并初始化 credits
            INSERT INTO user_balances (user_id, credits, created_at, updated_at) VALUES (NEW.uid, 0, now(), now());
        END IF;

        -- 更新 user_balances 表，扣除相应的 credits
        UPDATE user_balances
        SET credits = credits - NEW.credits, updated_at = now()
        WHERE user_id = NEW.uid;

        -- 返回新插入的行
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    `
	if err := tx.Exec(createFunctionSQL).Error; err != nil {
		return err
	}

	// 检查并创建触发器
	createTriggerSQL := `
    DO $$
    BEGIN
        -- 检查触发器是否存在
        IF NOT EXISTS (
            SELECT 1
            FROM pg_trigger
            WHERE tgname = 'trigger_deduct_or_create_user_credits'
        ) THEN
            -- 如果触发器不存在，则创建触发器
            CREATE TRIGGER trigger_deduct_or_create_user_credits
            AFTER INSERT ON llm_usage_reports
            FOR EACH ROW
            EXECUTE FUNCTION deduct_or_create_user_credits();
        END IF;
    END;
    $$;
    `
	if err := tx.Exec(createTriggerSQL).Error; err != nil {
		return err
	}

	return nil
}

func updateEmptyUUIDs(db *gorm.DB) error {
	// 更新 Workflow 表中空的 UUID
	var workflows []model.Workflow
	if err := db.Where("uuid = '' OR uuid IS NULL").Find(&workflows).Error; err != nil {
		return err
	}
	for _, workflow := range workflows {
		if err := db.Model(&workflow).Update("uuid", utils.GenerateUUID()).Error; err != nil {
			return err
		}
	}

	// 更新 WorkflowLog 表中空的 UUID
	var workflowLogs []model.WorkflowLog
	if err := db.Where("uuid = '' OR uuid IS NULL").Find(&workflowLogs).Error; err != nil {
		return err
	}
	for _, workflowLog := range workflowLogs {
		if err := db.Model(&workflowLog).Update("uuid", utils.GenerateUUID()).Error; err != nil {
			return err
		}
	}

	return nil
}
