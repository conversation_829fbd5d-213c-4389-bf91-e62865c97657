package order

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"aineuro_backend/model"
	"aineuro_backend/service/payment"
)

type OrderService struct {
	paymentProvider payment.PaymentProvider
	successURL      string
}

func NewOrderService(provider payment.PaymentProvider, successURL string) *OrderService {
	return &OrderService{
		paymentProvider: provider,
		successURL:      successURL,
	}
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(userID uint, points uint, amount uint) (*model.Order, error) {
	order := &model.Order{
		UserID:   userID,
		OrderNo:  fmt.Sprintf("ORD%s", uuid.New().String()[:8]),
		Amount:   amount,
		Points:   points,
		Status:   model.OrderStatusPending,
		Provider: s.paymentProvider.GetProvider(),
	}

	if err := model.CreateOrder(order); err != nil {
		return nil, fmt.Errorf("failed to create order: %v", err)
	}

	// 创建支付
	paymentReq := payment.PaymentRequest{
		OrderNo:    order.OrderNo,
		Amount:     amount,
		Points:     points,
		SuccessURL: s.successURL,
	}

	result, err := s.paymentProvider.CreatePayment(paymentReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create payment: %v", err)
	}
	if !result.Success {
		return nil, fmt.Errorf("failed to create payment: %v", result.Error)
	}

	// 更新订单支付信息
	order.PaymentURL = result.PaymentURL
	order.ProviderOrderID = result.ProviderOrderID
	if err := order.Save(); err != nil {
		return nil, fmt.Errorf("failed to update order: %v", err)
	}

	return order, nil
}

// HandleCallback 处理支付回调
func (s *OrderService) HandleCallback(ctx *gin.Context) error {
	// 验证签名
	valid, orderNo, err := s.paymentProvider.VerifyCallback(ctx)
	if err != nil {
		return fmt.Errorf("failed to verify callback: %v", err)
	}
	if !valid {
		return errors.New("invalid callback signature")
	}

	if orderNo == "" {
		return errors.New("order number not found in callback")
	}

	// 查找订单以获取积分数量
	order, err := model.FindUnpaidOrderByOrderNo(orderNo)
	if err != nil {
		return fmt.Errorf("order not found: %v", err)
	}

	// 在事务中更新订单状态和用户余额
	now := time.Now()
	if err := model.UpdateOrderStatusAndIncreaseBalance(orderNo, model.OrderStatusPaid, &now, float64(order.Points)); err != nil {
		return fmt.Errorf("failed to update order and balance: %v", err)
	}

	return nil
}
