package payment

import (
	"aineuro_backend/model"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type CreeemProvider struct {
	apiKey        string
	productID     string
	client        *resty.Client
	webhookSecret string
	apiEndpoint   string
}

type CreeemCheckoutResponse struct {
	ID          string `json:"id"`
	CheckoutURL string `json:"checkout_url"`
}

func NewCreeemProvider(apiKey, productID, webhookSecret, apiEndpoint string) *CreeemProvider {
	return &CreeemProvider{
		apiKey:        apiKey,
		productID:     productID,
		client:        resty.New(),
		webhookSecret: webhookSecret,
		apiEndpoint:   strings.TrimSuffix(apiEndpoint, "/"),
	}
}

func (p *CreeemProvider) CreatePayment(req PaymentRequest) (*PaymentResult, error) {
	var resp CreeemCheckoutResponse

	body := map[string]interface{}{
		"product_id": p.productID,
		"request_id": req.OrderNo,
	}
	if req.SuccessURL != "" {
		body["success_url"] = req.SuccessURL
	}

	response, err := p.client.R().
		SetHeader("x-api-key", p.apiKey).
		SetBody(body).
		SetResult(&resp).
		Post(p.apiEndpoint + "/v1/checkouts")

	if err != nil {
		return &PaymentResult{
			Success: false,
			Error:   fmt.Errorf("failed to create Creem checkout: %v", err),
		}, nil
	}

	if response.StatusCode() != 200 || resp.CheckoutURL == "" || resp.ID == "" {
		return &PaymentResult{
			Success: false,
			Error:   fmt.Errorf("failed to create Creem checkout: %v", response.String()),
		}, nil
	}

	return &PaymentResult{
		Success:         true,
		PaymentURL:      resp.CheckoutURL,
		ProviderOrderID: resp.ID,
	}, nil
}

func (p *CreeemProvider) VerifyCallback(ctx *gin.Context) (bool, string, error) {
	// from header : creem-signature
	signature := ctx.Request.Header.Get("creem-signature")
	if signature == "" {
		return false, "", fmt.Errorf("signature not found in header")
	}

	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		return false, "", fmt.Errorf("failed to read body: %v", err)
	}

	structData := struct {
		ID     string `json:"id"`
		Object struct {
			RequestID string `json:"request_id"`
			Order     struct {
				Amount  uint   `json:"amount"`
				Status  string `json:"status"`
				Product string `json:"product"`
			} `json:"order"`
		} `json:"object"`
	}{}

	if err := json.Unmarshal(body, &structData); err != nil {
		return false, "", fmt.Errorf("failed to unmarshal body: %v", err)
	}

	if structData.Object.Order.Status != "paid" {
		return false, "", fmt.Errorf("order status is not paid")
	}

	if !p.verifySignature(string(body), signature) {
		return false, "", fmt.Errorf("signature is invalid")
	}

	return true, structData.Object.RequestID, nil
}

// verifySignature Creem 特有的签名验证方法
func (p *CreeemProvider) verifySignature(body string, signature string) bool {
	// 计算签名
	h := hmac.New(sha256.New, []byte(p.webhookSecret))
	h.Write([]byte(body))
	computedSignature := hex.EncodeToString(h.Sum(nil))

	fmt.Printf("computedSignature: %s\nsignature: %s\n", computedSignature, signature)
	return hmac.Equal([]byte(computedSignature), []byte(signature))
}

func (p *CreeemProvider) GetProvider() model.PaymentProvider {
	return model.PaymentProviderCreem
}
