package payment

import (
	"aineuro_backend/model"

	"github.com/gin-gonic/gin"
)

// PaymentRequest 支付请求参数
type PaymentRequest struct {
	OrderNo    string
	Amount     uint
	Points     uint
	SuccessURL string
	CancelURL  string
}

// PaymentResult 支付结果
type PaymentResult struct {
	Success         bool
	PaymentURL      string
	ProviderOrderID string
	Error           error
}

// CallbackParams 支付回调参数
type CallbackParams map[string]string

// PaymentProvider 支付提供商接口
type PaymentProvider interface {
	GetProvider() model.PaymentProvider
	// CreatePayment 创建支付订单
	CreatePayment(req PaymentRequest) (*PaymentResult, error)

	// VerifyCallback 验证支付回调
	VerifyCallback(ctx *gin.Context) (bool, string, error)
}
