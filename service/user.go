package service

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
)

type CreateUserParams struct {
	Username string
	Email    string
	Password string
	Source   string
	GiveBalance int
}

func CreateUser(params CreateUserParams) (*model.User, error) {
	username := GetSourcePrefix(params.Source) + params.Username
	salt := utils.RandomSalt(16)
	password := utils.PasswordMd5(params.Password, salt)
	user := &model.User{
		Username: username,
		Email:    params.Email,
		Password: password,
		Salt:     salt,
	}
	if err := model.CreateUser(user); err != nil {
		return nil, err
	}

	if params.GiveBalance > 0 {
		model.UpdateUserBalance(user.ID, float64(params.GiveBalance))
	}

	user, err := model.GetUserByEmail(user.Email)
	if err != nil {
		return nil, err
	}
	go func ()  {
		utils.PushDeerNotify("[FlowAI] New User '" + user.Username + "' registered")
	}()
	return user, nil
}

func GetUserBySourceAndUsername(source, username string) (*model.User, error) {
	name := GetSourcePrefix(source) + username
	return model.GetUserByName(name)
}
