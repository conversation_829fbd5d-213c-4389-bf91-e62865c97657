package model

import "fmt"

// ModelNameExistsError represents an error when a model name already exists for a user
type ModelNameExistsError struct {
	ModelName string
	UserID    uint
}

func (e *ModelNameExistsError) Error() string {
	return fmt.Sprintf("model name '%s' already exists for user %d", e.ModelName, e.UserID)
}

// NewModelNameExistsError creates a new ModelNameExistsError
func NewModelNameExistsError(modelName string, userID uint) error {
	return &ModelNameExistsError{
		ModelName: modelName,
		UserID:    userID,
	}
}