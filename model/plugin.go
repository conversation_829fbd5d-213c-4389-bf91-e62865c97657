package model

type Plugin struct {
	Base
	Name      string `gorm:"not null;unique" json:"name"`
	Endpoint  string `gorm:"not null" json:"endpoint"`
	IsEnabled bool   `gorm:"not null" json:"is_enabled"`
	Version   string `gorm:"not null" json:"version"`
	Proto     string `gorm:"not null" json:"proto"`
}

func GetPluginByName(name string) (*Plugin, error) {
	var plugin Plugin
	err := DB.First(&plugin, "name = ?", name).Error
	return &plugin, err
}

func GetPluginUrl(name string) (string, error) {
	plugin, err := GetPluginByName(name)
	if err != nil {
		return "", err
	}
	return plugin.Endpoint, nil
}

func GetPlugins() ([]Plugin, error) {
	var plugins []Plugin
	err := DB.Where("is_enabled = ?", true).Find(&plugins).Error
	return plugins, err
}
