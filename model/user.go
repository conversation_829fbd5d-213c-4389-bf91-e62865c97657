package model

import (
	"aineuro_backend/utils"

	"gorm.io/gorm"
)

type User struct {
	Base
	Username string `gorm:"unique;not null"`
	Email    string `gorm:"index"`
	Phone    string `gorm:"index"`
	Password string `gorm:"not null"`
	Salt     string `gorm:"not null"`
}

func GetUser(id uint) (*User, error) {
	var user User
	err := DB.First(&user, id).Error
	return &user, err
}

func GetUserByName(username string) (*User, error) {
	var user User
	err := DB.Find(&user, "username = ?", username).First(&user).Error
	return &user, err
}

func GetUserByUsernameAndPwd(username, password string) (*User, error) {
	user, err := GetUserByName(username)
	if err != nil {
		return nil, err
	}

	if user.Password != utils.PasswordMd5(password, user.Salt) {
		return nil, gorm.ErrRecordNotFound
	}

	return user, nil
}

func UpdateUserBasicInfo(id uint, phone string) error {
	return DB.Model(&User{}).Where("id = ?", id).Update("phone", phone).Error
}

func UpdateUserPassword(id uint, SaltPassword string) error {
	return DB.Model(&User{}).Where("id = ?", id).Update("password", SaltPassword).Error
}

func GetUserByEmail(email string) (*User, error) {
	var user User
	err := DB.Where("email = ?", email).First(&user).Error
	return &user, err
}

func CreateUser(user *User) error {
	return DB.Create(user).Error
}

func GetUserByUsernameOrEmailAndPwd(usernameOrEmail, password string) (*User, error) {
	var users []User
	err := DB.Where("username = ? OR email = ?", usernameOrEmail, usernameOrEmail).Find(&users).Error
	if err != nil {
		return nil, err
	}

	for _, user := range users {
		if user.Password == utils.PasswordMd5(password, user.Salt) {
			return &user, nil
		}
	}

	return nil, gorm.ErrRecordNotFound
}
