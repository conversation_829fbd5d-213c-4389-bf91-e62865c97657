package model

import "aineuro_backend/utils"

type Workflow struct {
	Base
	UUID        string `gorm:"type:char(32);unique_index" json:"uuid"`
	Name        string `gorm:"not null" json:"name"`
	Description string `gorm:"not null" json:"description"`
	Data        string `gorm:"not null;type:text" json:"data"`
	UserID      uint   `gorm:"not null" json:"user_id"`
	User        *User  `gorm:"foreignKey:UserID" json:"user"`
}
func GetWorkflowByUUID(uuid string) (*Workflow, error) {
	var workflow Workflow
	err := DB.Where("uuid = ?", uuid).First(&workflow).Error
	return &workflow, err
}

func ListWorkflows(uid uint, page, pageSize int) ([]Workflow, error) {
	var workflows []Workflow
	err := DB.Omit("data").Where("user_id = ?", uid).Offset((page - 1) * pageSize).Limit(pageSize).Order("created_at desc").Find(&workflows).Error
	return workflows, err
}

func ListWorkflowsBySearch(query string, uid uint, page, pageSize int) ([]Workflow, error) {
	if query == "" {
		return ListWorkflows(uid, page, pageSize)
	}
	var workflows []Workflow
	query = "%" + query + "%"
	err := DB.Omit("data").
		Where("user_id = ? and (name ILIKE ? or description ILIKE ?)", uid, query, query).
		Offset((page - 1) * pageSize).Limit(pageSize).Order("created_at desc").Find(&workflows).Error
	return workflows, err
}

func CountWorkflows(uid uint) (int64, error) {
	var count int64
	err := DB.Model(&Workflow{}).Where("user_id = ?", uid).Count(&count).Error
	return count, err
}

func CountWorkflowsBySearch(query string, uid uint) (int64, error) {
	if query == "" {
		return CountWorkflows(uid)
	}
	var count int64
	err := DB.Model(&Workflow{}).
		Where("user_id = ? and (name ILIKE ? or description ILIKE ?)", uid, query, query).
		Or("description LIKE ?", "%"+query+"%").
		Count(&count).Error
	return count, err
}

func CreateWorkflow(workflow *Workflow) error {
	workflow.UUID = utils.GenerateUUID()
	return DB.Create(workflow).Error
}

func UpdateWorkflowBasicInfo(uuid string, name, description string) error {
	return DB.Model(&Workflow{}).Where("uuid = ?", uuid).Updates(map[string]interface{}{
		"name": name, "description": description,
	}).Error
}

func UpdateWorkflowData(uuid string, data string) error {
	return DB.Model(&Workflow{}).Where("uuid = ?", uuid).Update("data", data).Error
}

func DeleteWorkflow(uuid string) error {
	return DB.Where("uuid = ?", uuid).Delete(&Workflow{}).Error
}
