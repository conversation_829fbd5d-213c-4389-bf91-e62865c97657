package model

import (
	"aineuro_backend/utils"
	"time"

	"gorm.io/gorm"
)

type WorkflowLog struct {
	Base
	UUID       string    `gorm:"type:char(32);unique_index" json:"uuid"`
	WorkflowID uint      `gorm:"not null;index" json:"workflow_id"`
	Input      string    `gorm:"not null;type:text" json:"input"`
	Output     string    `gorm:"not null;type:text" json:"output"`
	Logs       string    `gorm:"not null;type:text" json:"logs"`
	UserID     uint      `gorm:"not null;index" json:"user_id"`
	Workflow   *Workflow `gorm:"foreignKey:WorkflowID" json:"workflow"`
}

func GetWorkflowLog(uuid string) (*WorkflowLog, error) {
	var log WorkflowLog
	err := DB.Preload("Workflow", func(db *gorm.DB) *gorm.DB {
		return db.Omit("data").Unscoped()
	}).Where("uuid = ?", uuid).First(&log).Error
	return &log, err
}

func ListWorkflowLogs(uid uint, page, pageSize int, days int) ([]WorkflowLog, error) {
	var logs []WorkflowLog
	db := DB.Where("user_id = ?", uid)
	
	if days > 0 {
		db = db.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}
	
	err := db.Omit("logs", "input", "output").
		Preload("Workflow", func(db *gorm.DB) *gorm.DB {
			return db.Omit("data").Unscoped()
		}).
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Order("created_at desc").
		Find(&logs).Error
	return logs, err
}

func CountWorkflowLogs(uid uint, days int) (int64, error) {
	var count int64
	db := DB.Model(&WorkflowLog{}).Where("user_id = ?", uid)
	
	if days > 0 {
		db = db.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}
	
	err := db.Count(&count).Error
	return count, err
}

func CreateWorkflowLog(log *WorkflowLog) error {
	if log.UUID == "" {
		log.UUID = utils.GenerateUUID()
	}
	return DB.Create(log).Error
}

func DeleteWorkflowLog(uuid string) error {
	return DB.Where("uuid = ?", uuid).Delete(&WorkflowLog{}).Error
}
