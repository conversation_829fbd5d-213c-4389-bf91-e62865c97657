package model

import "gorm.io/gorm"

type UserBalance struct {
	Base
	UserID  uint    `gorm:"not null" json:"user_id"`
	Credits float64 `gorm:"not null" json:"credits"`
}

func GetUserBalance(uid uint) (*UserBalance, error) {
	var balance UserBalance
	err := DB.First(&balance, "user_id = ?", uid).Error
	return &balance, err
}

func UpdateUserBalance(uid uint, credits float64) error {
	return DB.Where(UserBalance{UserID: uid}).
		Assign(UserBalance{Credits: credits}).
		FirstOrCreate(&UserBalance{}).Error
}

func IncreaseUserBalance(uid uint, credits float64) error {
	return DB.Model(&UserBalance{}).Where(UserBalance{UserID: uid}).
		Update("credits", gorm.Expr("credits + ?", credits)).Error
}
