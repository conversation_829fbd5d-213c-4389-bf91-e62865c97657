package model

import (
	"aineuro_backend/utils"
)

type ManualLLM struct {
	Base
	UUID                string `gorm:"type:char(32);unique_index" json:"uuid"`
	ModelName           string `gorm:"type:varchar(255);not null" json:"model_name"`
	ModelType           string `gorm:"type:varchar(255);not null" json:"model_type"` // openai, anthropic, etc.
	ModelEndpoint       string `gorm:"type:text;not null" json:"model_endpoint"`
	ModelKey            string `gorm:"type:text;not null" json:"model_key"`
	UserID              uint   `gorm:"not null;index" json:"-"`
	User                *User  `gorm:"foreignKey:UserID" json:"-"`
	ContentLimit        int    `gorm:"type:int;not null" json:"content_limit"`
	SupportFunctionCall bool   `gorm:"type:bool;not null" json:"support_function_call"`
	SupportJSONOutput   bool   `gorm:"type:bool;not null" json:"support_json_output"`
	SupportVision       bool   `gorm:"type:bool;not null;default:false" json:"support_vision"`
	IsOfficial          bool   `gorm:"type:bool;not null;default:false" json:"is_official"`
}

func GetManualLLM(uuid string) (*ManualLLM, error) {
	var ml ManualLLM
	err := DB.Preload("User").Where("uuid = ?", uuid).First(&ml).Error
	return &ml, err
}

func GetManualLLMByName(name string, uid uint) (*ManualLLM, error) {
	var ml ManualLLM
	err := DB.Preload("User").Where("model_name = ? and user_id = ?", name, uid).First(&ml).Error
	return &ml, err
}

func ListManualLLMs(uid uint, page, pageSize int) ([]ManualLLM, error) {
	var mls []ManualLLM
	err := DB.Preload("User").
		Where("user_id = ?", uid).
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Order("created_at desc").
		Find(&mls).Error
	return mls, err
}

func CreateManualLLM(ml *ManualLLM) error {
	ml.IsOfficial = false
	// 检查用户范围内的名称唯一性
	var count int64
	DB.Model(&ManualLLM{}).Where("user_id = ? AND model_name = ?", ml.UserID, ml.ModelName).Count(&count)
	if count > 0 {
		return NewModelNameExistsError(ml.ModelName, ml.UserID)
	}

	ml.UUID = utils.GenerateUUID()
	ml.Base.CreatedAt = utils.CurrentTime()
	ml.Base.UpdatedAt = utils.CurrentTime()
	return DB.Create(ml).Error
}

func UpdateManualLLM(ml *ManualLLM) error {
	// 检查用户范围内的名称唯一性（排除当前记录）
	var count int64
	DB.Model(&ManualLLM{}).Where("user_id = ? AND model_name = ? AND uuid != ?", ml.UserID, ml.ModelName, ml.UUID).Count(&count)
	if count > 0 {
		return NewModelNameExistsError(ml.ModelName, ml.UserID)
	}

	ml.Base.UpdatedAt = utils.CurrentTime()
	return DB.Save(ml).Error
}

func DeleteManualLLM(uuid string, uid uint) error {
	return DB.Where("uuid = ? and user_id = ? and is_official = false", uuid, uid).Delete(&ManualLLM{}).Error
}

func GetManualLLMsByUser(uid uint) ([]ManualLLM, error) {
	var mls []ManualLLM
	err := DB.Where("user_id = ?", uid).Find(&mls).Error
	return mls, err
}

func GetOfficialManualLLMs() ([]ManualLLM, error) {
	var mls []ManualLLM
	err := DB.Where("is_official = true").Order("model_name").Find(&mls).Error
	return mls, err
}
