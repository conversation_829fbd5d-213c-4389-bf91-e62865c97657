package model

import (
	"time"

	"gorm.io/gorm"
)

type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusPaid      OrderStatus = "paid"
	OrderStatusCancelled OrderStatus = "cancelled"
	OrderStatusFailed    OrderStatus = "failed"
)

type PaymentProvider string

const (
	PaymentProviderCreem PaymentProvider = "creem"
)

type Order struct {
	ID              uint            `gorm:"primarykey"`
	UserID          uint            `gorm:"not null"`
	OrderNo         string          `gorm:"uniqueIndex;not null"` // 订单号
	Amount          uint            `gorm:"not null"`             // 订单金额
	Points          uint            `gorm:"not null"`             // 购买的积分数量
	Status          OrderStatus     `gorm:"not null"`
	Provider        PaymentProvider `gorm:"not null"` // 支付提供商
	ProviderOrderID string          `gorm:"index"`    // 支付提供商的订单ID
	PaymentURL      string          // 支付URL
	PaidAt          *time.Time      // 支付时间
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func GetOrder(orderNo string) (*Order, error) {
	var order Order
	if err := DB.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// CreateOrder 创建订单
func CreateOrder(order *Order) error {
	return DB.Create(order).Error
}

// Save 保存订单
func (o *Order) Save() error {
	return DB.Save(o).Error
}

// FindOrderByOrderNo 通过订单号查找订单
func FindOrderByOrderNo(orderNo string) (*Order, error) {
	var order Order
	if err := DB.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

func FindUnpaidOrderByOrderNo(orderNo string) (*Order, error) {
	var order Order
	if err := DB.Where("order_no = ? AND status = ?", orderNo, OrderStatusPending).First(&order).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// UpdateOrderStatusAndIncreaseBalance 在事务中更新订单状态并增加用户余额
func UpdateOrderStatusAndIncreaseBalance(orderNo string, status OrderStatus, paidAt *time.Time, points float64) error {
	return DB.Transaction(func(tx *gorm.DB) error {
		// 查找并更新订单状态
		var order Order
		if err := tx.Where("order_no = ? AND status = ?", orderNo, OrderStatusPending).First(&order).Error; err != nil {
			return err
		}

		order.Status = status
		order.PaidAt = paidAt
		if err := tx.Save(&order).Error; err != nil {
			return err
		}

		// 增加用户余额
		if err := tx.Model(&UserBalance{}).
			Where(&UserBalance{UserID: order.UserID}).
			Update("credits", gorm.Expr("credits + ?", points)).Error; err != nil {
			return err
		}

		return nil
	})
}
