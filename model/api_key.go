package model

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"gorm.io/gorm"
)

// APIKey 模型用于存储用户的API Keys
type APIKey struct {
	Base
	UserID    uint      `gorm:"index;not null" json:"user_id"`
	Name      string    `gorm:"not null" json:"name"`       // API Key的名称
	Key       string    `gorm:"unique;not null" json:"key"` // API Key的值
	LastUsed  time.Time `json:"last_used"`                  // 最后一次使用时间
	ExpiresAt time.Time `json:"expires_at"`                 // 过期时间
}

// GenerateAPIKey 生成新的API Key
func GenerateAPIKey(userID uint, name string, expiresInDays int) (*APIKey, error) {
	// 生成随机字节
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return nil, err
	}

	// 转换为十六进制字符串
	keyString := hex.EncodeToString(bytes)

	// 创建APIKey记录
	apiKey := &APIKey{
		UserID:    userID,
		Name:      name,
		Key:       keyString,
		ExpiresAt: time.Now().AddDate(0, 0, expiresInDays),
	}

	// 保存到数据库
	if err := DB.Create(apiKey).Error; err != nil {
		return nil, err
	}

	return apiKey, nil
}

// GetAPIKeyByKey 通过Key获取APIKey
func GetAPIKeyByKey(key string) (*APIKey, error) {
	var apiKey APIKey
	err := DB.Where("key = ?", key).First(&apiKey).Error
	if err != nil {
		return nil, err
	}

	// 检查是否过期
	if time.Now().After(apiKey.ExpiresAt) {
		return nil, gorm.ErrRecordNotFound
	}

	// 更新最后使用时间
	DB.Model(&apiKey).Update("last_used", time.Now())

	return &apiKey, nil
}

// GetAPIKeysByUserID 获取用户的所有API Keys
func GetAPIKeysByUserID(userID uint) ([]APIKey, error) {
	var apiKeys []APIKey
	err := DB.Where("user_id = ?", userID).Find(&apiKeys).Error
	return apiKeys, err
}

// DeleteAPIKey 删除API Key
func DeleteAPIKey(id uint, userID uint) error {
	return DB.Where("id = ? AND user_id = ?", id, userID).Delete(&APIKey{}).Error
}
