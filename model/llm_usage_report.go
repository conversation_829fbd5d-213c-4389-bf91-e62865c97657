package model

import "time"

type LLMUsageReport struct {
	Base
	WorkflowID uint    `gorm:"not null" json:"workflow_id"`
	Uid        uint    `gorm:"not null" json:"uid"`
	LLMType    string  `gorm:"not null" json:"llm_type"`
	LLMInput   uint    `gorm:"not null" json:"llm_input"`
	LLMOutput  uint    `gorm:"not null" json:"llm_output"`
	Credits    float64 `gorm:"not null;default:0.00" json:"credits"`
}

func ReportLLMUsage(report *LLMUsageReport) error {
	err := DB.Create(report).Error
	if err != nil {
		panic(err)
	}
	return err
}

// GetUserCreditsHistory 获取用户历史7天的Credits消耗记录，支持分页
func GetUserCreditsHistory(uid uint, page, pageSize int) ([]LLMUsageReport, error) {
	var reports []LLMUsageReport

	// 计算7天前的时间
	sevenDaysAgo := time.Now().AddDate(0, 0, -7)

	// 计算偏移量
	offset := (page - 1) * pageSize

	err := DB.Where("uid = ? AND created_at >= ?", uid, sevenDaysAgo).
		Order("created_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&reports).Error

	return reports, err
}

// CountUserCreditsHistory 统计用户历史7天的Credits消耗记录总数
func CountUserCreditsHistory(uid uint) (int64, error) {
	var count int64

	// 计算7天前的时间
	sevenDaysAgo := time.Now().AddDate(0, 0, -7)

	err := DB.Model(&LLMUsageReport{}).
		Where("uid = ? AND created_at >= ?", uid, sevenDaysAgo).
		Count(&count).Error

	return count, err
}
