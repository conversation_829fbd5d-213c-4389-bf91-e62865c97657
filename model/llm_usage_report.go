package model

type LLMUsageReport struct {
	Base
	WorkflowID uint    `gorm:"not null" json:"workflow_id"`
	Uid        uint    `gorm:"not null" json:"uid"`
	LLMType    string  `gorm:"not null" json:"llm_type"`
	LLMInput   uint    `gorm:"not null" json:"llm_input"`
	LLMOutput  uint    `gorm:"not null" json:"llm_output"`
	Credits    float64 `gorm:"not null;default:0.00" json:"credits"`
}

func ReportLLMUsage(report *LLMUsageReport) error {
	err := DB.Create(report).Error
	if err != nil {
		panic(err)
	}
	return err
}
