package model

import (
	"time"
)

// VerifyCode 存储用户注册时的验证码信息
type VerifyCode struct {
	Base
	Email     string    `gorm:"index;not null"`
	Code      string    `gorm:"not null"`
	Username  string    `gorm:"not null"`
	Password  string    `gorm:"not null"`
	ExpireAt  time.Time `gorm:"not null"`
	LastSentAt *time.Time // 上次发送验证码的时间
	Used      bool      `gorm:"default:false"`
}

// CreateVerifyCode 创建一个新的验证码记录
func CreateVerifyCode(email, code, username, password string, expireAt time.Time) error {
	now := time.Now()
	verifyCode := &VerifyCode{
		Email:     email,
		Code:      code,
		Username:  username,
		Password:  password,
		ExpireAt:  expireAt,
		LastSentAt: &now,
		Used:      false,
	}
	return DB.Create(verifyCode).Error
}

// GetVerifyCode 根据邮箱和验证码获取验证信息
func GetVerifyCode(email, code string) (*VerifyCode, error) {
	var verifyCode VerifyCode
	err := DB.Where("email = ? AND code = ? AND used = ? AND expire_at > ?",
		email, code, false, time.Now()).First(&verifyCode).Error
	return &verifyCode, err
}

// MarkVerifyCodeAsUsed 将验证码标记为已使用
func MarkVerifyCodeAsUsed(id uint) error {
	return DB.Model(&VerifyCode{}).Where("id = ?", id).Update("used", true).Error
}

// GetActiveVerifyCodeByEmail 根据邮箱获取有效的验证码信息
func GetActiveVerifyCodeByEmail(email string) (*VerifyCode, error) {
	var verifyCode VerifyCode
	err := DB.Where("email = ? AND used = ? AND expire_at > ?",
		email, false, time.Now()).Order("created_at DESC").First(&verifyCode).Error
	return &verifyCode, err
}

// UpdateVerifyCodeLastSentTime 更新验证码的最后发送时间
func UpdateVerifyCodeLastSentTime(id uint) error {
	now := time.Now()
	return DB.Model(&VerifyCode{}).Where("id = ?", id).Update("last_sent_at", &now).Error
}

// CanResendVerifyCode 检查是否可以重新发送验证码（距离上次发送时间是否超过60秒）
func CanResendVerifyCode(id uint) (bool, time.Time, error) {
	var verifyCode VerifyCode
	err := DB.Where("id = ?", id).First(&verifyCode).Error
	if err != nil {
		return false, time.Time{}, err
	}

	// 如果没有最后发送时间，则可以发送
	if verifyCode.LastSentAt == nil {
		return true, time.Now(), nil
	}

	// 检查是否已过60秒
	cooldownPeriod := 60 * time.Second
	nextAllowedTime := verifyCode.LastSentAt.Add(cooldownPeriod)
	canResend := time.Now().After(nextAllowedTime)

	return canResend, nextAllowedTime, nil
}
