package model

import (
	"aineuro_backend/utils"
	"fmt"
	"strings"

	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func getDSN() string {
	host := utils.GetEnvWithDefault("DB_HOST", "*************")
	user := utils.GetEnvWithDefault("DB_USER", "postgres")
	password := utils.GetEnvWithDefault("DB_PASSWORD", "root")
	dbname := utils.GetEnvWithDefault("DB_NAME", "aineuro")
	port := utils.GetEnvWithDefault("DB_PORT", "5432")
	timezone := utils.GetEnvWithDefault("DB_TIMEZONE", "Asia/Shanghai")
	sslmode := utils.GetEnvWithDefault("DB_SSLMODE", "disable")

	// 使用 PostgreSQL URL 格式
	dsn := fmt.Sprintf("postgresql://%s:%s@%s:%s/%s?sslmode=%s&TimeZone=%s",
		user, password, host, port, dbname, sslmode, timezone)

	// 添加自定义选项
	if options := utils.GetEnvWithDefault("DB_OPTIONS", ""); options != "" {
		dsn = dsn + "&" + strings.ReplaceAll(options, " ", "&")
	}
	fmt.Println(dsn)
	return dsn
}

func Init() {
	dsn := getDSN()
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NowFunc: func() time.Time {
			ti, _ := time.LoadLocation(utils.GetEnvWithDefault("DB_TIMEZONE", "Asia/Shanghai"))
			return time.Now().In(ti)
		},
	})
	if err != nil {
		panic("无法连接数据库")
	}

	DB = db
}

type Base struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt utils.Time     `json:"created_at"`
	UpdatedAt utils.Time     `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type BaseNoDeletedAt struct {
	ID        uint       `gorm:"primarykey" json:"id"`
	CreatedAt utils.Time `json:"created_at"`
	UpdatedAt utils.Time `json:"updated_at"`
}
