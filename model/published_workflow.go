package model

import "aineuro_backend/utils"

// PublishedWorkflow 表示已发布的工作流版本
type PublishedWorkflow struct {
	BaseNoDeletedAt
	UUID        string    `gorm:"type:char(32);unique_index" json:"uuid"`
	WorkflowID  uint      `gorm:"not null;index" json:"workflow_id"`
	Workflow    *Workflow `gorm:"foreignKey:WorkflowID" json:"workflow"`
	Version     string    `gorm:"not null" json:"version"`
	Name        string    `gorm:"not null" json:"name"`
	Description string    `gorm:"not null" json:"description"`
	Data        string    `gorm:"not null;type:text" json:"data"`
	UserID      uint      `gorm:"not null" json:"user_id"`
	User        *User     `gorm:"foreignKey:UserID" json:"user"`
	IsActive    bool      `gorm:"not null;default:true" json:"is_active"`
	PublishedAt int64     `gorm:"not null" json:"published_at"`
}

// GetPublishedWorkflowByUUID 通过UUID获取发布版工作流
func GetPublishedWorkflowByUUID(uuid string) (*PublishedWorkflow, error) {
	var publishedWorkflow PublishedWorkflow
	err := DB.Where("uuid = ?", uuid).First(&publishedWorkflow).Error
	return &publishedWorkflow, err
}

func CheckPublishedWorkflowExists(workflow_id uint) (bool, error) {
	var count int64
	err := DB.Model(&PublishedWorkflow{}).Where("workflow_id = ?", workflow_id).Count(&count).Error
	return count > 0, err
}

// GetPublishedWorkflowByWorkflowIDAndVersion 通过原始工作流ID和版本号获取发布版工作流
func GetPublishedWorkflowByWorkflowIDAndVersion(workflowID uint, version string) (*PublishedWorkflow, error) {
	var publishedWorkflow PublishedWorkflow
	err := DB.Where("workflow_id = ? AND version = ?", workflowID, version).First(&publishedWorkflow).Error
	return &publishedWorkflow, err
}

// GetLatestPublishedWorkflowByWorkflowID 获取原始工作流的最新发布版本
func GetLatestPublishedWorkflowByWorkflowID(workflowID uint) (*PublishedWorkflow, error) {
	var publishedWorkflow PublishedWorkflow
	err := DB.Where("workflow_id = ? AND is_active = ?", workflowID, true).
		Order("published_at desc").First(&publishedWorkflow).Error
	return &publishedWorkflow, err
}

// ListPublishedWorkflows 列出用户的所有发布版工作流
func ListPublishedWorkflows(uid uint, page, pageSize int) ([]PublishedWorkflow, error) {
	var publishedWorkflows []PublishedWorkflow
	err := DB.Omit("data").
		Where("user_id = ?", uid).
		Offset((page - 1) * pageSize).Limit(pageSize).
		Order("published_at desc").Find(&publishedWorkflows).Error
	return publishedWorkflows, err
}

// ListPublishedWorkflowVersions 列出特定工作流的所有发布版本
func ListPublishedWorkflowVersions(workflowID uint, page, pageSize int) ([]PublishedWorkflow, error) {
	var publishedWorkflows []PublishedWorkflow
	err := DB.Omit("data").
		Where("workflow_id = ?", workflowID).
		Offset((page - 1) * pageSize).Limit(pageSize).
		Order("published_at desc").Find(&publishedWorkflows).Error
	return publishedWorkflows, err
}

// CountPublishedWorkflows 计算用户发布版工作流的数量
func CountPublishedWorkflows(uid uint) (int64, error) {
	var count int64
	err := DB.Model(&PublishedWorkflow{}).Where("user_id = ?", uid).Count(&count).Error
	return count, err
}

// CountPublishedWorkflowVersions 计算特定工作流的发布版本数量
func CountPublishedWorkflowVersions(workflowID uint) (int64, error) {
	var count int64
	err := DB.Model(&PublishedWorkflow{}).Where("workflow_id = ?", workflowID).Count(&count).Error
	return count, err
}

// CreatePublishedWorkflow 创建发布版工作流
func CreatePublishedWorkflow(publishedWorkflow *PublishedWorkflow) error {
	publishedWorkflow.UUID = utils.GenerateUUID()
	publishedWorkflow.PublishedAt = utils.NowUnix()
	return DB.Create(publishedWorkflow).Error
}

// UpdatePublishedWorkflowStatus 更新发布版工作流的状态
func UpdatePublishedWorkflowStatus(uuid string, isActive bool) error {
	return DB.Model(&PublishedWorkflow{}).Where("uuid = ?", uuid).Update("is_active", isActive).Error
}

// DeletePublishedWorkflow 删除发布版工作流
func DeletePublishedWorkflow(uuid string) error {
	return DB.Where("uuid = ?", uuid).Delete(&PublishedWorkflow{}).Error
}

// UpdatePublishedWorkflow 更新已存在的发布版工作流
func UpdatePublishedWorkflow(publishedWorkflow *PublishedWorkflow) error {
	publishedWorkflow.PublishedAt = utils.NowUnix()
	return DB.Model(&PublishedWorkflow{}).Where("workflow_id = ? AND version = ?",
		publishedWorkflow.WorkflowID, publishedWorkflow.Version).
		Updates(map[string]interface{}{
			"name":         publishedWorkflow.Name,
			"description":  publishedWorkflow.Description,
			"data":         publishedWorkflow.Data,
			"is_active":    publishedWorkflow.IsActive,
			"published_at": publishedWorkflow.PublishedAt,
		}).Error
}
