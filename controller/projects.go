package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func Projects(c *gin.Context) {
	page := cast.ToInt(c<PERSON><PERSON><PERSON>("page", "1"))
	pageSize := cast.ToInt(<PERSON><PERSON>("pageSize", "20"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 1
	}
	search := c.<PERSON>("search", "")
	uid := getUserID(c)
	workflows, err := model.ListWorkflowsBySearch(search, uid, page, pageSize)
	if err != nil {
		fail(c, 500, "list workflows failed")
		return
	}

	count, err := model.CountWorkflowsBySearch(search, uid)
	if err != nil {
		fail(c, 500, "count workflows failed")
		return
	}

	for i := range workflows {
		workflows[i].ID = 0
	}
	success(c, gin.H{
		"list":  workflows,
		"total": count,
	})
}

func ProjectCreate(c *gin.Context) {
	req := struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Data        string `json:"data"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	uid := getUserID(c)

	workflow := model.Workflow{
		UUID:        utils.GenerateUUID(),
		Name:        req.Name,
		Description: req.Description,
		Data:        req.Data,
		UserID:      uid,
	}

	if err := model.CreateWorkflow(&workflow); err != nil {
		fail(c, 500, "create workflow failed")
		return
	}

	success(c, workflow.UUID)
}

func ProjectUpdate(c *gin.Context) {
	req := struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Data        string `json:"data"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	uuid := c.Param("uuid")
	m, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, 400, "invalid workflow")
		return
	}

	err = model.UpdateWorkflowBasicInfo(m.UUID, req.Name, req.Description)
	if err != nil {
		fail(c, 500, "update workflow failed")
		return
	}

	if req.Data != "" {
		err = model.UpdateWorkflowData(m.UUID, req.Data)
		if err != nil {
			fail(c, 500, "update workflow data failed")
			return
		}
	}

	success(c, "ok")
}

func ProjectGet(c *gin.Context) {
	uuid := c.Param("uuid")
	m, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, 400, "invalid workflow")
		return
	}

	type ProjectGetResponse struct {
		*model.Workflow
		IsPublished bool `json:"is_published"`
	}

	isPublished := false
	if m.UUID != "" {
		exists, _ := model.CheckPublishedWorkflowExists(m.ID)
		isPublished = exists
	}

	m.ID = 0
	success(c, ProjectGetResponse{
		Workflow:    m,
		IsPublished: isPublished,
	})
}

func ProjectDelete(c *gin.Context) {
	uuid := c.Param("uuid")
	m, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, 400, "invalid workflow")
		return
	}

	// 检查是否存在关联的已发布工作流
	exists, err := model.CheckPublishedWorkflowExists(m.ID)
	if err != nil {
		fail(c, 500, "check published workflow failed")
		return
	}
	if exists {
		fail(c, 400, "cannot delete workflow with published versions")
		return
	}

	if err := model.DeleteWorkflow(m.UUID); err != nil {
		fail(c, 500, "delete workflow failed")
		return
	}

	success(c, "ok")
}

func ProjectClone(c *gin.Context) {
	uuid := c.Param("uuid")
	m, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, 400, "invalid workflow")
		return
	}

	uid := getUserID(c)

	workflow := model.Workflow{
		UUID:        utils.GenerateUUID(),
		Name:        m.Name + "_" + time.Now().Format("20060102150405"),
		Description: m.Description,
		Data:        m.Data,
		UserID:      uid,
	}

	if err := model.CreateWorkflow(&workflow); err != nil {
		fail(c, 500, "create workflow failed")
		return
	}

	success(c, workflow.UUID)
}
