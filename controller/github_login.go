package controller

import (
	"aineuro_backend/service"
	"aineuro_backend/utils"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/github"
	"gorm.io/gorm"
)

var (
	githubOauthConfig *oauth2.Config
)

func init() {
	githubOauthConfig = &oauth2.Config{
		ClientID:     "Ov23li9s5WRiFhWfWVsW",
		ClientSecret: "d53afd55fd41ecb347c73c8519bda9e2ecd3fa7b",
		RedirectURL:  "https://flowai.cc/v1/auth/github/callback",
		Scopes:       []string{"user:email"},
		Endpoint:     github.Endpoint,
	}
}

func GithubLogin(c *gin.Context) {
	url := githubOauthConfig.AuthCodeURL("state")
	c.<PERSON>(http.StatusOK, gin.H{
		"url": url,
	})
}

func GithubCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		fail(c, http.StatusBadRequest, "Authorization code not received")
		return
	}

	token, err := githubOauthConfig.Exchange(context.Background(), code)
	if err != nil {
		fail(c, http.StatusBadRequest, "Unable to get token")
		return
	}

	client := githubOauthConfig.Client(context.Background(), token)
	resp, err := client.Get("https://api.github.com/user")
	if err != nil {
		fail(c, http.StatusBadRequest, "Unable to get user information")
		return
	}
	defer resp.Body.Close()

	userData, err := io.ReadAll(resp.Body)
	if err != nil {
		fail(c, http.StatusBadRequest, "Unable to read user information")
		return
	}

	var userInfo struct {
		Email *string `json:"email"` // 使用指针类型，允许为null
		ID    int64   `json:"id"`
	}
	if err := json.Unmarshal(userData, &userInfo); err != nil {
		fail(c, http.StatusBadRequest, "Unable to parse user data")
		return
	}

	email := ""
	if userInfo.Email != nil {
		email = *userInfo.Email
	}

	// 检查用户是否存在,如果不存在则创建新用户
	user, err := service.GetUserBySourceAndUsername("github", strconv.FormatInt(userInfo.ID, 10))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			fail(c, http.StatusInternalServerError, "Failed to query user")
			return
		}

		// 用户不存在，创建新用户
		user, err = service.CreateUser(service.CreateUserParams{
			Username:    strconv.FormatInt(userInfo.ID, 10),
			Email:       email,
			Password:    utils.RandomSalt(18),
			Source:      "github",
			GiveBalance: 50,
		})
		if err != nil {
			fail(c, http.StatusInternalServerError, "Unable to create user")
			return
		}
	}

	// 登录用户
	sessionPut(c, "authenticated", user.ID)
	c.Redirect(http.StatusTemporaryRedirect, "/dashboard")
}
