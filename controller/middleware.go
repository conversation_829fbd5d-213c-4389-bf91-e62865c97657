package controller

import (
	"time"

	"aineuro_backend/model"

	"github.com/gin-gonic/gin"
)

func AuthMiddleware(c *gin.Context) {
	ctx := c.Request.Context()
	if !SessionManager.Exists(ctx, "authenticated") {
		fail(c, 401, "unauthorized")
		return
	}

	// token 过期时间自动续期
	deadline := SessionManager.Deadline(ctx)
	now := time.Now()

	// 如果剩余有效期少于7天，且今天还没有延期过，则延期
	if deadline.Sub(now) < 7*24*time.Hour {
		// 检查上次延期是否是今天
		if !sameDay(deadline.Add(-7*24*time.Hour), now) {
			newDeadline := now.Add(7 * 24 * time.Hour)
			SessionManager.SetDeadline(ctx, newDeadline)
		}
	}

	c.Next()
}

// 辅助函数：检查两个时间是否在同一天
func sameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

func UserCheckMiddleware(c *gin.Context) {
	uid := getUserID(c)
	if uid == 0 {
		fail(c, 401, "unauthorized")
		return
	}

	user, err := model.GetUser(uid)
	if err != nil {
		fail(c, 400, "invalid user")
		return
	}

	c.Set("user", user)
	c.Next()
}

func SystemMiddleware(c *gin.Context) {
	apiKey := c.GetHeader("X-API-KEY")
	if apiKey != "860ed2a6-2eca-4e2d-a1d5-9e70b84c8b7a" {
		fail(c, 401, "unauthorized")
		return
	}
	c.Next()
}

// APIKeyMiddleware 验证API Key并设置用户ID
func APIKeyMiddleware(c *gin.Context) {
	apiKey := c.GetHeader("X-API-KEY")
	if apiKey == "" {
		fail(c, 401, "API Key required")
		return
	}

	// 验证API Key
	key, err := model.GetAPIKeyByKey(apiKey)
	if err != nil {
		fail(c, 401, "Invalid API Key")
		return
	}

	// 设置用户ID
	user, err := model.GetUser(key.UserID)
	if err != nil {
		fail(c, 401, "Invalid user")
		return
	}

	c.Set("user", user)
	c.Next()
}
