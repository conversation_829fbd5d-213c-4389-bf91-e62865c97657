package controller

import (
	"aineuro_backend/service"
	"aineuro_backend/utils"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"gorm.io/gorm"
)

var (
	googleOauthConfig *oauth2.Config
)

func init() {
	googleOauthConfig = &oauth2.Config{
		ClientID:     "4029765645-vtuo9ur6vqi5h7khhq7qemjk83s0hps0.apps.googleusercontent.com",
		ClientSecret: "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",
		RedirectURL:  "https://flowai.cc/v1/auth/google/callback",
		Scopes:       []string{"https://www.googleapis.com/auth/userinfo.email"},
		Endpoint:     google.Endpoint,
	}
}

func GoogleLogin(c *gin.Context) {
	url := googleOauthConfig.AuthCodeURL("state")

	c.<PERSON>(http.StatusOK, gin.H{
		"url": url,
	})
}

func GoogleCallback(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		fail(c, http.StatusBadRequest, "Authorization code not received")
		return
	}

	token, err := googleOauthConfig.Exchange(context.Background(), code)
	if err != nil {
		fail(c, http.StatusBadRequest, "Unable to get token")
		return
	}

	client := googleOauthConfig.Client(context.Background(), token)
	resp, err := client.Get("https://www.googleapis.com/oauth2/v2/userinfo")
	if err != nil {
		fail(c, http.StatusBadRequest, "Unable to get user information")
		return
	}
	defer resp.Body.Close()

	userData, err := io.ReadAll(resp.Body)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Unable to read user data")
		return
	}
	var userInfo struct {
		Email string `json:"email"`
		ID    string `json:"id"`
	}
	if err := json.Unmarshal(userData, &userInfo); err != nil {
		fail(c, http.StatusInternalServerError, "Unable to parse user data")
		return
	}

	email := userInfo.Email
	email = removeDotsFromGmail(email) // 去除 Gmail 邮箱前缀中的点号

	// 检查用户是否存在,如果不存在则创建新用户
	user, err := service.GetUserBySourceAndUsername("google", userInfo.ID)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			fail(c, http.StatusInternalServerError, "Failed to query user")
			return
		}
		// 创建新用户
		user, err = service.CreateUser(service.CreateUserParams{
			Username:    userInfo.ID, // 使用 Google ID 作为用户名
			Email:       email,       // 使用 Google 账户邮箱作为邮箱
			Password:    utils.RandomSalt(18),
			Source:      "google",
			GiveBalance: 50, // google 登录赠送 50 积分
		})
		if err != nil {
			fail(c, http.StatusInternalServerError, "Unable to create user")
			return
		}
	}

	// 登录用户
	sessionPut(c, "authenticated", user.ID)
	c.Redirect(http.StatusTemporaryRedirect, "/dashboard")
}

// 添加新函数来处理 Gmail 邮箱
func removeDotsFromGmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 || parts[1] != "gmail.com" {
		return email
	}
	username := strings.Replace(parts[0], ".", "", -1)
	return username + "@gmail.com"
}
