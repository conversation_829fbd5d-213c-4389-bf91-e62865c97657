package controller

import (
	"aineuro_backend/model"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

const DEFAULT_SHOW_LOGS_DAYS = 15

func WorkflowLogs(c *gin.Context) {
	uid := getUserID(c)
	page := cast.ToInt(<PERSON><PERSON>("page", "1"))
	pageSize := cast.ToInt(<PERSON><PERSON>("pageSize", "10"))

	logs, err := model.ListWorkflowLogs(uid, page, pageSize, DEFAULT_SHOW_LOGS_DAYS)
	if err != nil {
		fail(c, 400, "invalid user")
		return
	}
	count, err := model.CountWorkflowLogs(uid, DEFAULT_SHOW_LOGS_DAYS)
	if err != nil {
		fail(c, 400, "invalid user")
		return
	}

	// hide id
	for i := range logs {
		logs[i].ID = 0
	}
	success(c, gin.H{
		"list":  logs,
		"total": count,
	})
}

func WorkflowLog(c *gin.Context) {
	uuid := c.Param("uuid")
	log, err := model.GetWorkflowLog(uuid)
	if err != nil {
		fail(c, 400, "invalid log")
		return
	}
	log.ID = 0
	success(c, log)
}

func WorkflowLogDelete(c *gin.Context) {
	uuid := c.Param("uuid")
	log, err := model.GetWorkflowLog(uuid)
	if err != nil {
		fail(c, 400, "invalid log")
		return
	}

	uid := getUserID(c)
	if log.UserID != uid {
		fail(c, 400, "invalid log")
		return
	}

	err = model.DeleteWorkflowLog(uuid)
	if err != nil {
		fail(c, 400, "failed to delete log")
		return
	}

	success(c, "success")
}
