package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// PublishWorkflow 将工作流发布为发布版工作流
func PublishWorkflow(c *gin.Context) {
	req := struct {
		ID          string `json:"id" binding:"required"`
		Version     string `json:"version" binding:"required"`
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}{}
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request format")
		return
	}

	// 获取原始工作流
	uuid := req.ID
	workflow, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Workflow does not exist")
		return
	}

	// 检查权限
	userID := getUserID(c)
	if workflow.UserID != userID {
		fail(c, http.StatusForbidden, "No permission to operate this workflow")
		return
	}

	// 检查版本号是否已存在
	existingWorkflow, err := model.GetPublishedWorkflowByWorkflowIDAndVersion(workflow.ID, req.Version)
	if err == nil {
		// 版本已存在，更新该版本
		publishedWorkflow := &model.PublishedWorkflow{
			WorkflowID:  workflow.ID,
			Version:     req.Version,
			Name:        req.Name,
			Description: req.Description,
			Data:        workflow.Data,
			UserID:      userID,
			IsActive:    true,
		}

		if err := model.UpdatePublishedWorkflow(publishedWorkflow); err != nil {
			fail(c, http.StatusInternalServerError, "Failed to update workflow version")
			return
		}

		success(c, gin.H{
			"message": "Workflow version updated successfully",
			"uuid":    existingWorkflow.UUID,
		})
		return
	}

	// 创建发布版工作流
	publishedWorkflow := &model.PublishedWorkflow{
		WorkflowID:  workflow.ID,
		Version:     req.Version,
		Name:        req.Name,
		Description: req.Description,
		Data:        workflow.Data,
		UserID:      userID,
		IsActive:    true,
	}

	if err := model.CreatePublishedWorkflow(publishedWorkflow); err != nil {
		log.Println(err)
		fail(c, http.StatusInternalServerError, "Failed to publish workflow")
		return
	}

	success(c, gin.H{
		"message": "Workflow published successfully",
		"uuid":    publishedWorkflow.UUID,
	})
}

// GetPublishedWorkflow 获取发布版工作流详情
func GetPublishedWorkflow(c *gin.Context) {
	uuid := c.Param("uuid")
	publishedWorkflow, err := model.GetPublishedWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Published workflow does not exist")
		return
	}

	// 检查权限
	userID := getUserID(c)
	if publishedWorkflow.UserID != userID {
		fail(c, http.StatusForbidden, "No permission to access this published workflow")
		return
	}

	publishedWorkflow.ID = 0

	success(c, publishedWorkflow)
}

// ListPublishedWorkflows 列出用户的所有发布版工作流
func ListPublishedWorkflows(c *gin.Context) {
	userID := getUserID(c)
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	// 获取数据
	publishedWorkflows, err := model.ListPublishedWorkflows(userID, page, pageSize)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get published workflow list")
		return
	}

	// 获取总数
	count, err := model.CountPublishedWorkflows(userID)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get published workflow count")
		return
	}

	type publishedWorkflow struct {
		UUID        string `json:"uuid"`
		Version     string `json:"version"`
		Name        string `json:"name"`
		IsActive    bool   `json:"is_active"`
		Description string `json:"description"`
	}

	list := make([]publishedWorkflow, len(publishedWorkflows))
	for i := 0; i < len(publishedWorkflows); i++ {
		list[i] = publishedWorkflow{
			UUID:        publishedWorkflows[i].UUID,
			Version:     publishedWorkflows[i].Version,
			Name:        publishedWorkflows[i].Name,
			IsActive:    publishedWorkflows[i].IsActive,
			Description: publishedWorkflows[i].Description,
		}
	}

	success(c, gin.H{
		"total": count,
		"list":  list,
	})
}

// ListWorkflowPublishedVersions 列出工作流的所有发布版本
func ListWorkflowPublishedVersions(c *gin.Context) {
	userID := getUserID(c)
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	// 获取已发布工作流
	uuid := c.Param("uuid")
	workflow, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Workflow does not exist")
		return
	}

	// 检查权限
	if workflow.UserID != userID {
		fail(c, http.StatusForbidden, "No permission to operate this workflow")
		return
	}

	// 获取版本列表
	versions, err := model.ListPublishedWorkflowVersions(workflow.ID, page, pageSize)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get version list")
		return
	}

	// 获取总数
	count, err := model.CountPublishedWorkflowVersions(workflow.ID)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get version count")
		return
	}

	type publishedVersion struct {
		ID        string     `json:"id"`
		Version   string     `json:"version"`
		Name      string     `json:"name"`
		IsActive  bool       `json:"is_active"`
		UpdatedAt utils.Time `json:"updated_at"`
	}

	publishedVersions := make([]publishedVersion, len(versions))
	for i := 0; i < len(versions); i++ {
		publishedVersions[i] = publishedVersion{
			ID:        versions[i].UUID,
			Version:   versions[i].Version,
			Name:      versions[i].Name,
			IsActive:  versions[i].IsActive,
			UpdatedAt: versions[i].UpdatedAt,
		}
	}

	success(c, gin.H{
		"total": count,
		"list":  publishedVersions,
	})
}

// UpdatePublishedWorkflowStatus 更新发布版工作流状态（激活/停用）
func UpdatePublishedWorkflowStatus(c *gin.Context) {
	req := struct {
		IsActive bool `json:"is_active"`
	}{}
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request format")
		return
	}

	uuid := c.Param("uuid")
	publishedWorkflow, err := model.GetPublishedWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Published workflow does not exist")
		return
	}

	// 检查权限
	userID := getUserID(c)
	if publishedWorkflow.UserID != userID {
		fail(c, http.StatusForbidden, "No permission to operate this published workflow")
		return
	}

	if err := model.UpdatePublishedWorkflowStatus(uuid, req.IsActive); err != nil {
		fail(c, http.StatusInternalServerError, "Failed to update status")
		return
	}

	success(c, gin.H{
		"message": "Status updated successfully",
	})
}

// DeletePublishedWorkflow 删除发布版工作流
func DeletePublishedWorkflow(c *gin.Context) {
	uuid := c.Param("uuid")
	publishedWorkflow, err := model.GetPublishedWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Published workflow does not exist")
		return
	}

	// 检查权限
	userID := getUserID(c)
	if publishedWorkflow.UserID != userID {
		fail(c, http.StatusForbidden, "No permission to operate this published workflow")
		return
	}

	if err := model.DeletePublishedWorkflow(uuid); err != nil {
		fail(c, http.StatusInternalServerError, "Failed to delete")
		return
	}

	success(c, gin.H{
		"message": "Deleted successfully",
	})
}
