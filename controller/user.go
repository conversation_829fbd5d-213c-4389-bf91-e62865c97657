package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/service"
	"aineuro_backend/utils"
	"aineuro_backend/utils/mail"
	"log"
	"strconv"

	"time"

	"github.com/gin-gonic/gin"
)

func Login(c *gin.Context) {
	req := struct {
		UsernameOrEmail string `json:"username_or_email"`
		Password        string `json:"password"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	user, err := model.GetUserByUsernameOrEmailAndPwd(req.UsernameOrEmail, req.Password)
	if err != nil {
		fail(c, 400, "invalid username/email or password")
		return
	}

	sessionPut(c, "authenticated", user.ID)
	success(c, gin.H{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
		"phone":    user.Phone,
	})
}

func Me(c *gin.Context) {
	userID := SessionManager.Get(c.Request.Context(), "authenticated").(uint)
	user, err := model.GetUser(userID)
	if err != nil {
		fail(c, 400, "invalid user")
		return
	}
	balance, _ := model.GetUserBalance(user.ID)
	if balance == nil {
		balance = &model.UserBalance{
			UserID:  user.ID,
			Credits: 0,
		}
	}
	credits := balance.Credits

	success(c, gin.H{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
		"phone":    user.Phone,
		"credits":  credits,
	})
}

func Logout(c *gin.Context) {
	SessionManager.Remove(c.Request.Context(), "authenticated")
	success(c, "ok")
}

func UserSettings(c *gin.Context) {
	req := struct {
		Phone       string `json:"phone"`
		OldPassword string `json:"old_password"`
		NewPassword string `json:"new_password"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	userID := SessionManager.Get(c.Request.Context(), "authenticated").(uint)
	// update Email and Phone
	err := model.UpdateUserBasicInfo(userID, req.Phone)
	if err != nil {
		fail(c, 400, "invalid user")
		return
	}

	if req.OldPassword != "" && req.NewPassword != "" {
		// check old password
		user, err := model.GetUser(userID)
		if err != nil {
			fail(c, 400, "invalid user")
			return
		}
		if user.Password != utils.PasswordMd5(req.OldPassword, user.Salt) {
			fail(c, 400, "invalid old password")
			return
		}
		// update Password
		newPassword := utils.PasswordMd5(req.NewPassword, user.Salt)
		err = model.UpdateUserPassword(userID, newPassword)
		if err != nil {
			fail(c, 400, "invalid user")
			return
		}
	}

	success(c, "ok")
}

// Register 处理用户注册请求
func Register(c *gin.Context) {
	req := struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
		Lang     string `json:"lang"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	// Check if email already exists
	_, err := model.GetUserByEmail(req.Email)
	if err == nil {
		fail(c, 400, "email already registered")
		return
	}

	// 检查是否已有未使用的验证码
	// 注意：GetActiveVerifyCodeByEmail 只返回未过期的验证码
	_, err = model.GetActiveVerifyCodeByEmail(req.Email)
	if err == nil {
		// 已有有效期内的验证码，不允许再次注册
		fail(c, 400, "verification code sent to your email, please complete verification or use resend feature")
		return
	}

	// 生成验证码
	code := mail.GenerateVerificationCode()

	// 生成激活链接
	link := "https://flowai.cc/dashboard/verify-email?email=" + req.Email + "&code=" + code

	// 存储验证信息到数据库
	expireTime := time.Now().Add(10 * time.Minute) // 10分钟有效期
	userName := utils.GenerateUUID()[:12]
	err = model.CreateVerifyCode(req.Email, code, userName, req.Password, expireTime)
	if err != nil {
		fail(c, 500, "create verification record failed")
		return
	}

	// 发送验证邮件
	go func() {
		// 根据用户语言选择邮件模板
		mailLang := mail.MAIL_LANG_ZH // 默认使用中文
		switch req.Lang {
		case "en":
			mailLang = mail.MAIL_LANG_EN
		}

		// 发送邮件
		err = mail.SendRegActiveMail(mailLang, req.Email, link, code)
		if err != nil {
			log.Println("send verification email failed:", err)
		}
	}()

	success(c, "verification code sent to your email, please complete verification within 10 minutes")
}

// VerifyEmail 验证邮箱验证码
func VerifyEmail(c *gin.Context) {
	req := struct {
		Email string `json:"email" binding:"required,email"`
		Code  string `json:"code" binding:"required"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	// 从数据库获取验证信息
	verifyInfo, err := model.GetVerifyCode(req.Email, req.Code)
	if err != nil {
		fail(c, 400, "invalid verification code or expired")
		return
	}

	// 创建用户
	user, err := service.CreateUser(service.CreateUserParams{
		Username:    verifyInfo.Username,
		Email:       verifyInfo.Email,
		Password:    verifyInfo.Password,
		Source:      "flowai",
		GiveBalance: 5, // 邮箱注册送5积分
	})

	if err != nil {
		fail(c, 500, "create user failed")
		return
	}

	// 将验证码标记为已使用
	err = model.MarkVerifyCodeAsUsed(verifyInfo.ID)
	if err != nil {
		log.Println("mark verification code as used failed:", err)
	}

	// 自动登录
	sessionPut(c, "authenticated", user.ID)

	success(c, gin.H{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
		"phone":    user.Phone,
		"message":  "register success",
	})
}

// ResendVerifyCode 重新发送验证码
func ResendVerifyCode(c *gin.Context) {
	req := struct {
		Email string `json:"email" binding:"required,email"`
		Lang  string `json:"lang"`
	}{}

	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, "invalid request body")
		return
	}

	// 检查邮箱是否已存在
	_, err := model.GetUserByEmail(req.Email)
	if err == nil {
		fail(c, 400, "email already registered, please login directly")
		return
	}

	// 检查是否有有效的验证码
	verifyCode, err := model.GetActiveVerifyCodeByEmail(req.Email)
	if err != nil {
		fail(c, 400, "verification code not found, please register again")
		return
	}

	// 检查是否可以重新发送（距离上次发送是否超过60秒）
	canResend, nextAllowedTime, err := model.CanResendVerifyCode(verifyCode.ID)
	if err != nil {
		fail(c, 500, "check resend status failed")
		return
	}

	if !canResend {
		// 计算还需要等待多少秒
		waitSeconds := int(time.Until(nextAllowedTime).Seconds()) + 1
		fail(c, 429, "Please wait " + strconv.Itoa(waitSeconds) + " seconds before resending")
		return
	}

	// 生成激活链接
	link := "https://flowai.cc/dashboard/verify-email?email=" + req.Email + "&code=" + verifyCode.Code

	// 更新最后发送时间
	err = model.UpdateVerifyCodeLastSentTime(verifyCode.ID)
	if err != nil {
		fail(c, 500, "update verification code status failed")
		return
	}

	// 发送验证邮件
	go func() {
		// 根据用户语言选择邮件模板
		mailLang := mail.MAIL_LANG_ZH // 默认使用中文
		switch req.Lang {
		case "en":
			mailLang = mail.MAIL_LANG_EN
		}

		// 发送邮件
		err = mail.SendRegActiveMail(mailLang, req.Email, link, verifyCode.Code)
		if err != nil {
			log.Println("resend verification email failed:", err)
		}
	}()

	success(c, "verification code resent to your email, please complete verification within 10 minutes")
}
