package controller

import (
	"aineuro_backend/model"
	"bytes"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func PluginCall(c *gin.Context) {
	pluginName := c.Param("plugin")
	endpoint, err := model.GetPluginUrl(pluginName)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req := struct {
		WorkflowID string         `json:"workflow_id"`
		Input      map[string]any `json:"input"`
	}{}

	if err = c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	wf, err := model.GetWorkflowByUUID(req.WorkflowID)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	pluginReq := struct {
		UserID     string         `json:"user_id"`
		Input      map[string]any `json:"input"`
		WorkflowID string         `json:"workflow_id"`
	}{
		UserID:     cast.ToString(wf.UserID),
		Input:      req.Input,
		WorkflowID: cast.ToString(wf.ID),
	}

	body, err := json.Marshal(pluginReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	r, err := http.NewRequestWithContext(c.Request.Context(), "POST", endpoint, bytes.NewBuffer(body))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	r.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(r)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	body, err = io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.Data(http.StatusOK, "application/json", body)
}
