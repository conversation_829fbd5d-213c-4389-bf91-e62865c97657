package controller

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func sessionPut(c *gin.Context, key string, value interface{}) {
	SessionManager.Put(c.Request.Context(), key, value)
}

func success(c *gin.Context, data interface{}) {
	c.<PERSON>(http.StatusOK, gin.H{
		"code": 0,
		"data": data,
	})
	c.Abort()
}

func fail(c *gin.Context, statusCode int, msg string) {
	log.Println("fail", msg)
	c.J<PERSON>(statusCode, gin.H{
		"code": -1,
		"data": nil,
		"msg":  msg,
	})
	c.Abort()
}

func getUserID(c *gin.Context) uint {
	return SessionManager.Get(c.Request.Context(), "authenticated").(uint)
}

func getPage(c *gin.Context) int {
	return cast.ToInt(<PERSON><PERSON>("page", "1"))
}

func getPageSize(c *gin.Context) int {
	return cast.ToInt(<PERSON><PERSON>("page_size", "10"))
}

