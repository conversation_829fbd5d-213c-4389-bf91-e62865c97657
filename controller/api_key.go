package controller

import (
	"aineuro_backend/model"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ListAPIKeys 获取用户的所有API Keys
func ListAPIKeys(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	apiKeys, err := model.GetAPIKeysByUserID(user.ID)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Get API Keys failed")
		return
	}

	type APIKeyResponse struct {
		ID        uint   `json:"id"`
		Name      string `json:"name"`
		Key       string `json:"key"`
		CreatedAt string `json:"created_at"`
		ExpiresAt string `json:"expires_at"`
		LastUsed  string `json:"last_used"`
	}

	var response []APIKeyResponse
	for _, key := range apiKeys {

		response = append(response, APIKeyResponse{
			ID:        key.ID,
			Name:      key.Name,
			Key:       key.Key,
			CreatedAt: key.CreatedAt.String(),
			ExpiresAt: key.ExpiresAt.String(),
			LastUsed:  key.LastUsed.String(),
		})
	}

	success(c, response)
}

// CreateAPIKey 创建新的API Key
func CreateAPIKey(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	type CreateAPIKeyRequest struct {
		Name          string `json:"name" binding:"required"`
		ExpiresInDays int    `json:"expires_in_days"`
	}

	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Default expires in 90 days
	if req.ExpiresInDays <= 0 {
		req.ExpiresInDays = 90
	}

	apiKey, err := model.GenerateAPIKey(user.ID, req.Name, req.ExpiresInDays)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Create API Key failed")
		return
	}

	success(c, gin.H{
		"id":         apiKey.ID,
		"name":       apiKey.Name,
		"key":        apiKey.Key,
		"created_at": apiKey.CreatedAt,
		"expires_at": apiKey.ExpiresAt,
	})
}

// DeleteAPIKey 删除API Key
func DeleteAPIKey(c *gin.Context) {
	user := c.MustGet("user").(*model.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		fail(c, http.StatusBadRequest, "Invalid ID")
		return
	}

	if err := model.DeleteAPIKey(uint(id), user.ID); err != nil {
		fail(c, http.StatusInternalServerError, "Delete API Key failed")
		return
	}

	success(c, gin.H{"message": "API Key deleted"})
}
