package controller

import (
	"aineuro_backend/utils"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// UploadImage 处理图片上传
func UploadImage(c *gin.Context) {
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		fail(c, 400, "No file uploaded")
		return
	}
	defer file.Close()

	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
	}

	if !allowedExts[ext] {
		fail(c, 400, "Invalid file type. Only images are allowed")
		return
	}

	// 上传文件到 R2
	url, err := utils.UploadFile(c.Request.Context(), file, header.Filename)
	if err != nil {
		fail(c, 500, "Failed to upload file: " + err.Error())
		return
	}

	success(c, url)
} 