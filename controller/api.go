package controller

import (
	"aineuro_backend/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

// APIWorkflowRun 通过API运行发布版工作流
func APIWorkflowRun(c *gin.Context) {
	uuid := c.Param("uuid")
	user := c.MustGet("user").(*model.User)

	type WorkflowRunRequest struct {
		Input   map[string]any `json:"input"`
		Stream  bool           `json:"stream"`
		Version string         `json:"version"` // 可选，如果不指定则使用最新活跃版本
	}

	var req WorkflowRunRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid input")
		return
	}

	var publishedWorkflow *model.PublishedWorkflow
	var err error

	// 如果提供了版本号，则获取指定版本
	if req.Version != "" {
		// 先获取原始工作流
		workflow, err := model.GetWorkflowByUUID(uuid)
		if err != nil {
			fail(c, http.StatusNotFound, "Workflow not found")
			return
		}
		if workflow.UserID != user.ID {
			fail(c, http.StatusNotFound, "Workflow not found")
			return
		}

		// 根据原始工作流ID和版本号获取发布版本
		publishedWorkflow, err = model.GetPublishedWorkflowByWorkflowIDAndVersion(workflow.ID, req.Version)
		if err != nil {
			fail(c, http.StatusNotFound, "Published workflow version not found")
			return
		}
	} else {
		// 直接根据UUID获取发布版工作流
		// 这种情况下通常是直接访问已知的发布版本UUID
		publishedWorkflow, err = model.GetPublishedWorkflowByUUID(uuid)
		if err != nil {
			// 如果没找到，尝试获取最新发布版本
			workflow, err := model.GetWorkflowByUUID(uuid)
			if err != nil {
				fail(c, http.StatusNotFound, "Workflow not found")
				return
			}
			if workflow.UserID != user.ID {
				fail(c, http.StatusNotFound, "Workflow not found")
				return
			}

			// 获取最新的发布版本
			publishedWorkflow, err = model.GetLatestPublishedWorkflowByWorkflowID(workflow.ID)
			if err != nil {
				fail(c, http.StatusNotFound, "No published version found")
				return
			}
		}
	}

	// 验证发布版工作流的所有权
	if publishedWorkflow.UserID != user.ID {
		fail(c, http.StatusNotFound, "Published workflow not found")
		return
	}

	// 检查是否激活
	if !publishedWorkflow.IsActive {
		fail(c, http.StatusBadRequest, "This published workflow version is inactive")
		return
	}

	// 使用发布版工作流的数据运行
	tempWorkflow := &model.Workflow{
		Data: publishedWorkflow.Data,
	}
	tempWorkflow.ID = publishedWorkflow.WorkflowID

	// 不记录日志，使用请求中的流选项
	workflowCommonHandler(c, user.ID, req.Input, "", tempWorkflow, req.Stream)
}
