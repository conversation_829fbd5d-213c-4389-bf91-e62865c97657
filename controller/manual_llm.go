package controller

import (
	"aineuro_backend/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

func ListManualLLMs(c *gin.Context) {
	uid := getUserID(c)
	page := getPage(c)
	pageSize := getPageSize(c)

	mls, err := model.ListManualLLMs(uint(uid), page, pageSize)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get manual LLM list")
		return
	}

	success(c, gin.H{"manual_llms": mls})
}

func ListOfficialLLMs(c *gin.Context) {
	mls, err := model.GetOfficialManualLLMs()
	if err != nil {
		fail(c, http.StatusInternalServerError, "Failed to get official manual LLM list")
		return
	}
	for i := 0; i < len(mls); i++ {
		mls[i].ModelKey = ""
		mls[i].ModelEndpoint = ""
	}

	success(c, gin.H{"official_llms": mls})
}

func CreateManualLLM(c *gin.Context) {
	var ml model.ManualLLM
	if err := c.ShouldBindJSON(&ml); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	ml.UserID = getUserID(c)
	if err := model.CreateManualLLM(&ml); err != nil {
		if _, ok := err.(*model.ModelNameExistsError); ok {
			fail(c, http.StatusConflict, "Model name already exists for this user")
		} else {
			fail(c, http.StatusInternalServerError, "Failed to create manual LLM")
		}
		return
	}

	success(c, gin.H{"manual_llm": ml})
}

func UpdateManualLLM(c *gin.Context) {
	uuid := c.Param("uuid")
	ml, err := model.GetManualLLM(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Manual LLM not found")
		return
	}
	originalIsOfficial := ml.IsOfficial

	if ml.UserID != getUserID(c) {
		fail(c, http.StatusForbidden, "No permission to modify this manual LLM")
		return
	}

	if err := c.ShouldBindJSON(ml); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 保持 is_official 不变
	ml.IsOfficial = originalIsOfficial
	ml.UserID = getUserID(c)

	if err := model.UpdateManualLLM(ml); err != nil {
		if _, ok := err.(*model.ModelNameExistsError); ok {
			fail(c, http.StatusConflict, "Model name already exists for this user")
		} else {
			fail(c, http.StatusInternalServerError, "Failed to update manual LLM")
		}
		return
	}

	success(c, gin.H{"manual_llm": ml})
}

func DeleteManualLLM(c *gin.Context) {
	uuid := c.Param("uuid")
	uid := getUserID(c)

	if err := model.DeleteManualLLM(uuid, uint(uid)); err != nil {
		fail(c, http.StatusInternalServerError, "Failed to delete manual LLM")
		return
	}

	success(c, gin.H{"message": "Manual LLM deleted"})
}

func GetManualLLM(c *gin.Context) {
	uuid := c.Param("uuid")
	ml, err := model.GetManualLLM(uuid)
	if err != nil {
		fail(c, http.StatusNotFound, "Manual LLM not found")
		return
	}

	if ml.UserID != getUserID(c) {
		fail(c, http.StatusForbidden, "No permission to view this manual LLM")
		return
	}

	success(c, gin.H{"manual_llm": ml})
}
