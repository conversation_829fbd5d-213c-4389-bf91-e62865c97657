package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// 基本HTTP认证中间件
func AdminAuthMiddleware(c *gin.Context) {
	// 获取 Authorization 请求头
	user, password, hasAuth := c.Request.BasicAuth()

	// 检查认证信息是否存在
	if !hasAuth {
		c.Header("WWW-Authenticate", "Basic realm=Admin Console")
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	// 从环境变量获取管理员凭据，如果不存在则使用默认值
	adminUser := utils.GetEnvWithDefault("ADMIN_USERNAME", "admin")
	adminPassword := utils.GetEnvWithDefault("ADMIN_PASSWORD", "admin123")

	// 验证用户名和密码
	if user != adminUser || password != adminPassword {
		c.<PERSON><PERSON>("WWW-Authenticate", "Basic realm=Admin Console")
		c.AbortWithStatus(http.StatusUnauthorized)
		return
	}

	c.Next()
}

// 用于存储每天统计数据的结构体
type DailyStats struct {
	Date          string  `json:"date"`
	UserCount     int64   `json:"user_count"`
	CreditsUsed   float64 `json:"credits_used"`
	CallCount     int64   `json:"call_count"`
	FormattedDate string  `json:"formatted_date"`
}

// 管理员控制台主页
func AdminDashboard(c *gin.Context) {
	// 获取今天的开始时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 统计今日注册用户数
	var todayUsers int64
	model.DB.Model(&model.User{}).Where("created_at >= ?", today).Count(&todayUsers)

	// 统计总用户数
	var totalUsers int64
	model.DB.Model(&model.User{}).Count(&totalUsers)

	// 统计今日积分消耗量
	var todayCredits float64
	model.DB.Model(&model.LLMUsageReport{}).Where("created_at >= ?", today).
		Select("SUM(credits)").Row().Scan(&todayCredits)

	// 获取过去7天的数据
	// 计算7天前的日期
	sevenDaysAgo := today.AddDate(0, 0, -6) // 包括今天在内的7天

	// 初始化7天的统计数据
	var dailyStats = make([]DailyStats, 0, 7)
	for i := 0; i < 7; i++ {
		date := sevenDaysAgo.AddDate(0, 0, i)
		nextDate := date.AddDate(0, 0, 1)

		// 当天的用户注册数
		var userCount int64
		model.DB.Model(&model.User{}).
			Where("created_at >= ? AND created_at < ?", date, nextDate).
			Count(&userCount)

		// 当天的积分消耗
		var creditsUsed float64
		model.DB.Model(&model.LLMUsageReport{}).
			Where("created_at >= ? AND created_at < ?", date, nextDate).
			Select("COALESCE(SUM(credits), 0)").Row().Scan(&creditsUsed)

		// 当天的调用次数
		var callCount int64
		model.DB.Model(&model.LLMUsageReport{}).
			Where("created_at >= ? AND created_at < ?", date, nextDate).
			Count(&callCount)

		// 添加到统计数据中
		dailyStats = append(dailyStats, DailyStats{
			Date:          date.Format("2006-01-02"),
			UserCount:     userCount,
			CreditsUsed:   creditsUsed,
			CallCount:     callCount,
			FormattedDate: date.Format("01-02"),
		})
	}

	// 处理分页
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize := 20
	offset := (page - 1) * pageSize

	// 获取用户总数用于计算总页数
	var totalCount int64
	model.DB.Model(&model.User{}).Count(&totalCount)

	totalPages := int(math.Ceil(float64(totalCount) / float64(pageSize)))

	// 获取分页的用户列表
	var users []struct {
		model.User
		Credits float64
	}

	model.DB.Model(&model.User{}).
		Select("users.*, COALESCE(user_balances.credits, 0) as credits").
		Joins("LEFT JOIN user_balances ON users.id = user_balances.user_id").
		Order("users.id DESC").
		Limit(pageSize).
		Offset(offset).
		Scan(&users)

	// 使用HTML模板渲染页面
	c.HTML(http.StatusOK, "admin_dashboard.html", gin.H{
		"todayUsers":   todayUsers,
		"totalUsers":   totalUsers,
		"todayCredits": todayCredits,
		"users":        users,
		"dailyStats":   dailyStats,
		"currentPage":  page,
		"totalPages":   totalPages,
		"hasNext":      page < totalPages,
		"hasPrev":      page > 1,
		"nextPage":     page + 1,
		"prevPage":     page - 1,
	})
}
