package controller

import (
	"aineuro_backend/model"
	"fmt"
	"math"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type LLMUsageReport struct {
	WorkflowID uint   `json:"workflow_id"`
	UserID     uint   `json:"user_id"`
	LLMType    string `json:"llm_type"`
	LLMInput   uint   `json:"llm_input"`
	LLMOutput  uint   `json:"llm_output"`
	ShouldBill *bool   `json:"should_bill"`
}

var llmCostPerK = map[string]float64{
	"gpt-4o":            1,
	"gpt-4o-mini":       0.06,
	"gpt-o3-mini":       0.4,
	"gpt-3.5-turbo":     0.16,
	"gpt-4":             7,
	"deepseek-chat":     0.1,
	"deepseek-reasoner": 0.2,
	"claude-3-5-sonnet": 1.4,
	"claude-3-7-sonnet": 1.4,
	"gpt-4.1":			 0.7,
	"gpt-4.1-mini":		 0.14,
	"gpt-4.1-nano":      0.035,
}

func calcCost(llmType string, totalTokens uint) (float64, error) {
	k := float64(totalTokens) / 1000
	fmt.Println("k", k)
	cost := llmCostPerK[llmType] * k
	fmt.Println("cost", cost, llmCostPerK[llmType])
	// cost 向上取整，最小0.01
	cost = math.Ceil(cost*100) / 100
	return cost, nil
}

func calcCredits(llmType string, inputTokens uint, outputTokens uint) (float64, error) {
	totalTokens := inputTokens + outputTokens

	if strings.HasPrefix(llmType, "gpt-4o-mini") {
		return calcCost("gpt-4o-mini", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-3.5-turbo") {
		return calcCost("gpt-3.5-turbo", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-4o") {
		return calcCost("gpt-4o", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-4.1-mini") {
		return calcCost("gpt-4.1-mini", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-4.1-nano") {
		return calcCost("gpt-4.1-nano", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-4.1") {
		return calcCost("gpt-4.1", totalTokens)
	}
	if strings.HasPrefix(llmType, "gpt-4") {
		return calcCost("gpt-4", totalTokens)
	}
	if strings.HasPrefix(llmType, "claude-3-5-sonnet") {
		return calcCost("claude-3-5-sonnet", totalTokens)
	}
	if strings.HasPrefix(llmType, "claude-3-7-sonnet") {
		return calcCost("claude-3-7-sonnet", totalTokens)
	}
	if strings.HasPrefix(llmType, "deepseek-chat") {
		return calcCost("deepseek-chat", totalTokens)
	}
	if strings.HasPrefix(llmType, "deepseek-reasoner") {
		return calcCost("deepseek-reasoner", totalTokens)
	}

	return 0, fmt.Errorf("unknown llm type: %s", llmType)

}

func ReportLLMUsage(c *gin.Context) {
	req := LLMUsageReport{}
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, 400, err.Error())
		return
	}
	cost := 0.0

	// 如果需要计费，则计算积分
	if req.ShouldBill != nil && *req.ShouldBill {
		cost, _ = calcCredits(req.LLMType, req.LLMInput, req.LLMOutput)
	}
	

	report := &model.LLMUsageReport{
		WorkflowID: req.WorkflowID,
		Uid:        req.UserID,
		LLMType:    req.LLMType,
		LLMInput:   req.LLMInput,
		LLMOutput:  req.LLMOutput,
		Credits:    cost,
	}

	err := model.ReportLLMUsage(report)
	if err != nil {
		fail(c, 500, err.Error())
		return
	}

	success(c, "ok")
}

func UserBalanceCheckForLLM(c *gin.Context) {
	userID := cast.ToUint(c.Query("user_id"))
	llmType := c.Query("llm_type")
	if userID == 0 || llmType == "" {
		fail(c, 400, "Bad request")
		return
	}
	inputTokens := cast.ToUint(c.Query("input_tokens"))
	outputTokens := uint(math.Ceil(float64(inputTokens) * 0.8)) // 默认输出是输入的80%

	cost, err := calcCredits(llmType, inputTokens, outputTokens)
	if err != nil {
		fail(c, 400, err.Error())
		return
	}
	balance, _ := model.GetUserBalance(userID)
	if balance.Credits < cost {
		fail(c, 400, "Insufficient balance")
		return
	}

	success(c, "ok")
}

// GetUserCreditsHistory 获取用户历史7天的Credits消耗记录
func GetUserCreditsHistory(c *gin.Context) {
	uid := getUserID(c)

	// 获取分页参数
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("pageSize", "10"))

	// 参数验证
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取Credits消耗记录
	reports, err := model.GetUserCreditsHistory(uid, page, pageSize)
	if err != nil {
		fail(c, 500, "获取Credits消耗记录失败")
		return
	}

	// 获取总记录数
	total, err := model.CountUserCreditsHistory(uid)
	if err != nil {
		fail(c, 500, "获取记录总数失败")
		return
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	success(c, gin.H{
		"data": reports,
		"pagination": gin.H{
			"page":        page,
			"pageSize":    pageSize,
			"total":       total,
			"totalPages":  totalPages,
			"hasNext":     page < totalPages,
			"hasPrev":     page > 1,
		},
	})
}
