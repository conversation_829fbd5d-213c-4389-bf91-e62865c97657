package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 准备工作流请求
func prepareWorkflowRequest(userID uint, input map[string]any, workflowModel *model.Workflow) ([]byte, []model.ManualLLM, error) {
	// 获取用户的LLM设置
	manualLLMs, err := model.GetManualLLMsByUser(userID)
	if err != nil {
		return nil, nil, err
	}
	officialManualLLMs, _ := model.GetOfficialManualLLMs()
	manualLLMs = append(manualLLMs, officialManualLLMs...)

	// 准备请求体
	runnerReq := struct {
		Data       string            `json:"data"`
		Input      map[string]any    `json:"input"`
		WorkflowID string            `json:"workflow_id"`
		UserID     string            `json:"user_id"`
		ManualLLMs []model.ManualLLM `json:"manual_llms"`
	}{
		Data:       workflowModel.Data,
		Input:      input,
		WorkflowID: workflowModel.UUID,
		UserID:     cast.ToString(userID),
		ManualLLMs: manualLLMs,
	}

	// 序列化请求
	body, err := json.Marshal(runnerReq)
	if err != nil {
		return nil, nil, err
	}

	return body, manualLLMs, nil
}

// 处理工作流日志记录
func logWorkflowExecution(workflowID uint, userID uint, input map[string]any, output string, logs string, uuid string) {
	inputText, err := json.Marshal(input)
	if err != nil {
		log.Println("Unable to serialize input data:", err)
		return
	}
	if logs == "" {
		logs = "[]"
	}
	err = model.CreateWorkflowLog(&model.WorkflowLog{
		WorkflowID: workflowID,
		Input:      string(inputText),
		Output:     output,
		Logs:       logs,
		UserID:     userID,
		UUID:       uuid,
	})
	if err != nil {
		log.Println("Failed to create workflow log:", err)
	}
}

// 处理事件数据
func handleEventData(eventName string, eventData string, outputBuffer *bytes.Buffer) {
	if eventName == "output" || eventName == "error" {
		outputBuffer.WriteString(eventData)
	}
}

// 处理流式事件
func sendStreamEvent(c *gin.Context, eventName string, eventData string, outputBuffer *bytes.Buffer) error {
	handleEventData(eventName, eventData, outputBuffer)
	_, err := c.Writer.Write([]byte(fmt.Sprintf("event:%s\ndata:%s\n\n", eventName, eventData)))
	return err
}

// 工作流处理主函数
func workflowCommonHandler(c *gin.Context, userID uint, input map[string]any, logID string, workflowModel *model.Workflow, streamOutput bool) {
	// 准备请求
	body, _, err := prepareWorkflowRequest(uint(userID), input, workflowModel)
	if err != nil {
		fail(c, http.StatusInternalServerError, "Prepare request failed: "+err.Error())
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(c.Request.Context(), "POST", WorkflowStreamRunner, bytes.NewBuffer(body))
	if err != nil {
		fail(c, http.StatusBadRequest, "Create request failed	")
		return
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		fail(c, http.StatusBadRequest, "Send request failed")
		return
	}
	defer resp.Body.Close()

	// 处理响应
	if streamOutput {
		handleStreamOutput(c, resp.Body, logID, workflowModel.ID, input, uint(userID))
	} else {
		handleBlockOutput(c, resp.Body, logID, workflowModel.ID, input, uint(userID))
	}
}

// 处理流式输出
func handleStreamOutput(c *gin.Context, responseBody io.ReadCloser, logID string, workflowModelID uint, input map[string]any, userID uint) {
	// 设置SSE头部
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	if logID != "" {
		c.Header("X-Log-ID", logID)
	}

	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		fail(c, http.StatusBadRequest, "Streaming output not supported")
		return
	}

	var outputBuffer bytes.Buffer
	clientGone := c.Request.Context().Done()

	// 配置日志记录, 如果logID为空, 则不记录日志
	if logID != "" {
		defer func() {
			logWorkflowExecution(workflowModelID, userID, input, outputBuffer.String(), "", logID)
		}()
	}

	// 创建scanner处理响应
	scanner := bufio.NewScanner(responseBody)
	buf := make([]byte, 0, 1024*1024) // 1MB buffer
	scanner.Buffer(buf, 1024*1024)    // 1MB max token size

	var eventBuffer bytes.Buffer
	var eventName string

	// 处理事件流
	for scanner.Scan() {
		// 检查客户端是否断开连接
		select {
		case <-clientGone:
			log.Println("Client disconnected, stopping workflow processing")
			return
		default:
			// 继续处理
		}

		line := scanner.Text()
		if strings.HasPrefix(line, "event:") {
			eventName = strings.TrimSpace(line[6:])
		} else if line == "" {
			eventData := eventBuffer.String()
			eventBuffer.Reset()

			if len(eventData) > 5 {
				err := sendStreamEvent(c, eventName, eventData[5:], &outputBuffer)
				if err != nil {
					fail(c, http.StatusInternalServerError, "Failed to send event")
					return
				}
				flusher.Flush()
			}
		} else {
			eventBuffer.WriteString(line + "\n")
		}
	}

	// 处理扫描错误
	if err := scanner.Err(); err != nil {
		sendStreamEvent(c, "error", `{"error":"Connection interrupted"}`, &outputBuffer)
		flusher.Flush()
	}
}

// 处理非流输出
func handleBlockOutput(c *gin.Context, responseBody io.ReadCloser, logID string, workflowModelID uint, input map[string]any, userID uint) {
	var outputBuffer bytes.Buffer
	clientGone := c.Request.Context().Done()

	// 配置日志记录, 如果logID为空, 则不记录日志
	if logID != "" {
		c.Header("X-Log-ID", logID)
		defer func() {
			logWorkflowExecution(workflowModelID, userID, input, outputBuffer.String(), "", logID)
		}()
	}

	// 创建scanner处理响应
	scanner := bufio.NewScanner(responseBody)
	buf := make([]byte, 0, 1024*1024) // 1MB buffer
	scanner.Buffer(buf, 1024*1024)    // 1MB max token size

	var eventBuffer bytes.Buffer
	var eventName string

	// 处理事件流
	for scanner.Scan() {
		// 检查客户端是否断开连接
		select {
		case <-clientGone:
			log.Println("Client disconnected, stopping workflow processing")
			return
		default:
			// 继续处理
		}

		line := scanner.Text()
		if strings.HasPrefix(line, "event:") {
			eventName = strings.TrimSpace(line[6:])
		} else if line == "" {
			eventData := eventBuffer.String()
			eventBuffer.Reset()

			// 收集事件数据
			if len(eventData) > 5 {
				data := eventData[5:]
				handleEventData(eventName, data, &outputBuffer)
			}
		} else {
			eventBuffer.WriteString(line + "\n")
		}
	}

	// 处理扫描错误
	if err := scanner.Err(); err != nil {
		handleEventData("error", "Connection interrupted", &outputBuffer)
	}

	// 返回收集的事件数据
	var output any
	json.Unmarshal(outputBuffer.Bytes(), &output)
	c.JSON(http.StatusOK, output)
}

// 执行工作流
func WorkflowRun(c *gin.Context) {
	req := struct {
		Input map[string]any `json:"input"`
	}{}
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request format")
		return
	}

	uuid := c.Param("uuid")
	wf, err := model.GetWorkflowByUUID(uuid)
	if err != nil {
		fail(c, http.StatusBadRequest, "Workflow does not exist")
		return
	}
	userID := getUserID(c)
	logID := utils.GenerateUUID() // 生成日志ID
	workflowCommonHandler(c, userID, req.Input, logID, wf, true)
}

// 调试工作流
func WorkflowDebug(c *gin.Context) {
	req := struct {
		Data  string         `json:"data"`
		Input map[string]any `json:"input"`
	}{}
	if err := c.ShouldBindJSON(&req); err != nil {
		fail(c, http.StatusBadRequest, "Invalid request format")
		return
	}
	userID := getUserID(c)
	logID := "" // 为空表示不记录日志
	workflowCommonHandler(c, userID, req.Input, logID, &model.Workflow{
		Data: req.Data,
	}, true)
}
