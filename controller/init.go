package controller

import (
	"aineuro_backend/model"
	"aineuro_backend/utils"
	"encoding/json"
	"html/template"
	"net/http"
	"time"

	"github.com/alexedwards/scs/gormstore"
	"github.com/alexedwards/scs/v2"
	"github.com/gin-gonic/gin"
)

var Router *gin.Engine
var SessionManager *scs.SessionManager

var WorkflowRunner = utils.GetEnvWithDefault("WORKFLOW_RUNNER", "http://127.0.0.1:8080/run")
var WorkflowStreamRunner = utils.GetEnvWithDefault("WORKFLOW_STREAM_RUNNER", "http://127.0.0.1:8080/run/stream")

func Init() {
	SessionManager = scs.New()
	SessionManager.Lifetime = 24 * time.Hour * 7
	store, err := gormstore.New(model.DB)
	if err != nil {
		panic(err)
	}
	SessionManager.Store = store

	router := gin.Default()

	// 添加模板函数
	router.SetFuncMap(template.FuncMap{
		"marshalJSON": func(v interface{}) template.JS {
			bytes, err := json.Marshal(v)
			if err != nil {
				return template.JS("[]")
			}
			return template.JS(bytes)
		},
	})

	Router = router
}

func Start(addr string) error {
	return http.ListenAndServe(addr, SessionManager.LoadAndSave(Router))
}
