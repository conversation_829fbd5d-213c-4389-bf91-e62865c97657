package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"aineuro_backend/model"
	"aineuro_backend/service/order"
	"aineuro_backend/utils"
)

type OrderController struct {
	orderService map[model.PaymentProvider]*order.OrderService
}

func NewOrderController(orderService map[model.PaymentProvider]*order.OrderService) *OrderController {
	return &OrderController{
		orderService: orderService,
	}
}

type CreateOrderRequest struct {
	Points   uint                  `json:"points" binding:"required,min=1"`
	Provider model.PaymentProvider `json:"provider" binding:"required"`
}

// CreateOrder 创建订单
func (c *OrderController) CreateOrder(ctx *gin.Context) {
	var req CreateOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		fail(ctx, http.StatusBadRequest, err.Error())
		return
	}

	// 从上下文中获取用户ID
	user, ok := ctx.Get("user")
	if !ok {
		fail(ctx, http.StatusUnauthorized, "unauthorized")
		return
	}
	userID := user.(*model.User).ID

	var points, amount uint
	priceTable := c.GetPriceTable(req.Provider)
	for _, price := range priceTable {
		if price.Points == req.Points {
			points = price.Points
			amount = price.Amount
			break
		}
	}
	if points == 0 || amount == 0 {
		fail(ctx, http.StatusBadRequest, "invalid points")
		return
	}

	order, err := c.orderService[req.Provider].CreateOrder(userID, points, amount)
	if err != nil {
		fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	success(ctx, gin.H{
		"order_no":    order.OrderNo,
		"amount":      order.Amount,
		"points":      order.Points,
		"payment_url": order.PaymentURL,
	})
}

func (c *OrderController) GetOrder(ctx *gin.Context) {
	orderNo := ctx.Param("order_no")
	order, err := model.GetOrder(orderNo)
	if err != nil {
		fail(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	user, ok := ctx.Get("user")
	if !ok {
		fail(ctx, http.StatusUnauthorized, "unauthorized")
		return
	}
	if order.UserID != user.(*model.User).ID {
		fail(ctx, http.StatusUnauthorized, "unauthorized")
		return
	}

	success(ctx, gin.H{
		"order_no":    order.OrderNo,
		"amount":      order.Amount,
		"points":      order.Points,
		"status":      order.Status,
		"payment_url": order.PaymentURL,
		"paid_at":     order.PaidAt,
		"created_at":  order.CreatedAt,
	})
}

func (c *OrderController) GetPriceTable(provider model.PaymentProvider) []PriceTable {
	return priceTable[provider]
}

// HandleCallback 处理支付回调
func (c *OrderController) HandleCallback(provider model.PaymentProvider) func(ctx *gin.Context) {
	return func(ctx *gin.Context) {

		if err := c.orderService[provider].HandleCallback(ctx); err != nil {
			fail(ctx, http.StatusBadRequest, err.Error())
			return
		}

		go func() {
			utils.PushDeerNotify("[FlowAI] New Order paid")
		}()
		success(ctx, gin.H{"message": "success"})
	}
}
