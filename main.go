package main

import (
	"aineuro_backend/assets"
	"aineuro_backend/controller"
	"aineuro_backend/model"
	"aineuro_backend/service/order"
	"aineuro_backend/service/payment"
	"aineuro_backend/utils"
	"net/http"
	"time"

	"log"

	"github.com/gin-gonic/gin"
)

var orderService map[model.PaymentProvider]*order.OrderService
var orderController *controller.OrderController

func init() {
	// 设置时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Printf("加载时区失败: %v", err)
	}
	time.Local = loc

	// 初始化 R2 客户端
	if err := utils.InitR2Client(); err != nil {
		log.Printf("初始化 R2 客户端失败: %v", err)
	}

	// 初始化数据库
	model.Init()
	// 初始化控制器
	controller.Init()

	// 初始化支付提供商
	creemProvider := payment.NewCreeemProvider(
		utils.GetEnvWithDefault("CREE_API_KEY", "creem_test_6qaABw143RQSeAU9E0h4KP"),
		utils.GetEnvWithDefault("CREE_PRODUCT_ID", "prod_5MuZvVkusLJB8xjghrpv5J"),
		utils.GetEnvWithDefault("CREE_WEBHOOK_SECRET", "whsec_4RylFHQdf87I0WpqkVf5vL"),
		utils.GetEnvWithDefault("CREE_API_ENDPOINT", "https://test-api.creem.io"),
	)
	orderService = map[model.PaymentProvider]*order.OrderService{
		model.PaymentProviderCreem: order.NewOrderService(
			creemProvider,
			utils.GetEnvWithDefault("CREEM_SUCCESS_URL", ""),
		),
	}
	orderController = controller.NewOrderController(orderService)
}

func main() {

	// 使用嵌入的模板
	assets.LoadEmbeddedTemplates(controller.Router)

	// 管理员控制台路由
	admin := controller.Router.Group("/admin", controller.AdminAuthMiddleware)
	{
		admin.GET("/dashboard", controller.AdminDashboard)
	}

	v1 := controller.Router.Group("/v1")
	// no login
	{
		v1.POST("/login", controller.Login)
		v1.POST("/register", controller.Register)
		v1.POST("/verify_email", controller.VerifyEmail)
		v1.POST("/resend_verify_code", controller.ResendVerifyCode)
		// v1.POST("/test", func(c *gin.Context) {
		// 	req := struct {
		// 		UserID     string         `json:"user_id"`
		// 		WorkflowID string         `json:"workflow_id"`
		// 		Input      map[string]any `json:"input"`
		// 	}{}

		// 	if err := c.ShouldBindJSON(&req); err != nil {
		// 		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		// 		return
		// 	}

		// 	c.JSON(http.StatusOK, gin.H{
		// 		"output": map[string]any{
		// 			"hello": "hello " + req.Input["editor"].(string),
		// 		},
		// 		"req": req,
		// 	})

		// })
		v1.GET("/auth/google", controller.GoogleLogin)
		v1.GET("/auth/google/callback", controller.GoogleCallback)
		v1.GET("/auth/github", controller.GithubLogin)
		v1.GET("/auth/github/callback", controller.GithubCallback)
		v1.POST("/order/creem/callback", orderController.HandleCallback(model.PaymentProviderCreem))
	}

	// with login
	{
		auth := v1.Group("", controller.AuthMiddleware, controller.UserCheckMiddleware)
		auth.GET("/me", controller.Me)
		auth.POST("/logout", controller.Logout)
		auth.GET("/projects", controller.Projects)
		auth.POST("/projects", controller.ProjectCreate)
		auth.DELETE("/projects/:uuid", controller.ProjectDelete)
		auth.GET("/projects/:uuid", controller.ProjectGet)
		auth.GET("/projects/:uuid/published_versions", controller.ListWorkflowPublishedVersions)
		auth.PATCH("/projects/:uuid", controller.ProjectUpdate)
		auth.POST("/projects_clone/:uuid", controller.ProjectClone)
		auth.POST("/workflow/runtime/project/:uuid", controller.WorkflowRun)
		auth.POST("/workflow/runtime/debug", controller.WorkflowDebug)
		auth.GET("/workflow/logs", controller.WorkflowLogs)
		auth.GET("/workflow/logs/:uuid", controller.WorkflowLog)
		auth.DELETE("/workflow/logs/:uuid", controller.WorkflowLogDelete)

		auth.GET("/published_workflows", controller.ListPublishedWorkflows)
		auth.POST("/published_workflows", controller.PublishWorkflow)
		auth.GET("/published_workflows/:uuid", controller.GetPublishedWorkflow)
		auth.PATCH("/published_workflows/:uuid/status", controller.UpdatePublishedWorkflowStatus)
		auth.DELETE("/published_workflows/:uuid", controller.DeletePublishedWorkflow)

		auth.POST("/user/settings", controller.UserSettings)
		auth.GET("/plugins", controller.GetPlugins)
		auth.GET("/manual_llms", controller.ListManualLLMs)
		auth.GET("/official_llms", controller.ListOfficialLLMs)
		auth.POST("/manual_llms", controller.CreateManualLLM)
		auth.GET("/manual_llms/:uuid", controller.GetManualLLM)
		auth.PATCH("/manual_llms/:uuid", controller.UpdateManualLLM)
		auth.DELETE("/manual_llms/:uuid", controller.DeleteManualLLM)
		auth.POST("/upload/image", controller.UploadImage)

		auth.POST("/order/create", orderController.CreateOrder)
		auth.GET("/order/:order_no", orderController.GetOrder)

		auth.GET("/api_keys", controller.ListAPIKeys)
		auth.POST("/api_keys", controller.CreateAPIKey)
		auth.DELETE("/api_keys/:id", controller.DeleteAPIKey)

		auth.GET("/credits/history", controller.GetUserCreditsHistory)
	}

	{
		system := v1.Group("", controller.SystemMiddleware)
		system.GET("/ping", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
			})
		})
		system.GET("/plugin_call/:plugin", controller.PluginCall)
		system.POST("/llm_usage_report", controller.ReportLLMUsage)
		system.GET("/user_balance_check_for_llm", controller.UserBalanceCheckForLLM)
	}

	// Public API 路由
	{
		api := v1.Group("/api", controller.APIKeyMiddleware)
		api.POST("/workflow/run/:uuid", controller.APIWorkflowRun)
	}

	err := controller.Start(":8999")
	if err != nil {
		panic(err)
	}
}
