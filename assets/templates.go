package assets

import (
	"embed"
	"html/template"
	"io/fs"

	"github.com/gin-gonic/gin"
)

//go:embed templates
var templatesFS embed.FS

// LoadEmbeddedTemplates 加载嵌入的HTML模板
func LoadEmbeddedTemplates(router *gin.Engine) {
	// 创建一个子文件系统，只包含templates目录
	subFS, err := fs.Sub(templatesFS, "templates")
	if err != nil {
		panic(err)
	}

	// 从嵌入的文件系统加载模板
	templ := template.Must(template.New("").Funcs(router.FuncMap).ParseFS(subFS, "*.html"))

	// 设置HTML渲染器
	router.SetHTMLTemplate(templ)
}
