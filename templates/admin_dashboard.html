<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>管理员控制台</title>
		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<style>
			body {
				font-family: Arial, sans-serif;
				line-height: 1.6;
				margin: 0;
				padding: 20px;
				background-color: #f5f5f5;
			}
			.container {
				max-width: 1200px;
				margin: 0 auto;
				background-color: #fff;
				padding: 20px;
				border-radius: 5px;
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
			}
			h1 {
				color: #333;
				border-bottom: 2px solid #eee;
				padding-bottom: 10px;
			}
			.stats-container {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				margin-bottom: 30px;
			}
			.stat-card {
				flex: 1;
				min-width: 200px;
				margin: 10px;
				padding: 20px;
				background-color: #f9f9f9;
				border-radius: 5px;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
				text-align: center;
			}
			.stat-card h2 {
				margin-top: 0;
				color: #555;
				font-size: 16px;
			}
			.stat-card .value {
				font-size: 36px;
				font-weight: bold;
				color: #2a8aca;
			}
			table {
				width: 100%;
				border-collapse: collapse;
				margin-top: 20px;
			}
			th,
			td {
				padding: 12px 15px;
				text-align: left;
				border-bottom: 1px solid #ddd;
			}
			th {
				background-color: #f2f2f2;
				font-weight: bold;
			}
			tr:hover {
				background-color: #f5f5f5;
			}
			.credits {
				font-weight: bold;
				color: #28a745;
			}
			.date {
				color: #666;
				font-size: 14px;
			}
			.charts-container {
				display: flex;
				flex-wrap: wrap;
				margin-bottom: 30px;
			}
			.chart-card {
				flex: 1;
				min-width: 45%;
				margin: 10px;
				padding: 15px;
				background-color: #fff;
				border-radius: 5px;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
			}
			.pagination {
				display: flex;
				justify-content: center;
				margin: 20px 0;
			}
			.pagination a,
			.pagination span {
				color: #333;
				padding: 8px 16px;
				text-decoration: none;
				border: 1px solid #ddd;
				margin: 0 4px;
				cursor: pointer;
			}
			.pagination a:hover {
				background-color: #f5f5f5;
			}
			.pagination .active {
				background-color: #2a8aca;
				color: white;
				border: 1px solid #2a8aca;
			}
			.pagination .disabled {
				color: #aaa;
				cursor: not-allowed;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<h1>管理员控制台</h1>

			<div class="stats-container">
				<div class="stat-card">
					<h2>今日新增用户</h2>
					<div class="value">{{ .todayUsers }}</div>
				</div>
				<div class="stat-card">
					<h2>总用户数</h2>
					<div class="value">{{ .totalUsers }}</div>
				</div>
				<div class="stat-card">
					<h2>今日积分消耗</h2>
					<div class="value">{{ printf "%.2f" .todayCredits }}</div>
				</div>
			</div>

			<h2>最近7天统计</h2>
			<div class="charts-container">
				<div class="chart-card">
					<canvas id="userChart"></canvas>
				</div>
				<div class="chart-card">
					<canvas id="creditsChart"></canvas>
				</div>
			</div>

			<div class="charts-container">
				<div class="chart-card">
					<canvas id="callsChart"></canvas>
				</div>
			</div>

			<h2>用户列表</h2>
			<table>
				<thead>
					<tr>
						<th>ID</th>
						<th>用户名</th>
						<th>邮箱</th>
						<th>电话</th>
						<th>注册时间</th>
						<th>积分余额</th>
					</tr>
				</thead>
				<tbody>
					{{ range .users }}
					<tr>
						<td>{{ .ID }}</td>
						<td>{{ .Username }}</td>
						<td>{{ .Email }}</td>
						<td>{{ .Phone }}</td>
						<td class="date">{{ .CreatedAt.Format "2006-01-02 15:04:05" }}</td>
						<td class="credits">{{ printf "%.2f" .Credits }}</td>
					</tr>
					{{ end }}
				</tbody>
			</table>

			<!-- 分页 -->
			<div class="pagination">
				{{ if .hasPrev }}
				<a href="?page={{ .prevPage }}">&laquo; 上一页</a>
				{{ else }}
				<span class="disabled">&laquo; 上一页</span>
				{{ end }}

				<!-- 显示当前页码和总页数 -->
				<span class="active">{{ .currentPage }}</span>
				<span>/</span>
				<span>{{ .totalPages }}</span>

				{{ if .hasNext }}
				<a href="?page={{ .nextPage }}">下一页 &raquo;</a>
				{{ else }}
				<span class="disabled">下一页 &raquo;</span>
				{{ end }}
			</div>
		</div>

		<script>
			try {
				// 后端传来的数据
				const dailyStats = {{ marshalJSON .dailyStats }};

				if (Array.isArray(dailyStats) && dailyStats.length > 0) {
					// 提取数据用于图表
					const dates = dailyStats.map((stat) => stat.formatted_date);
					const userCounts = dailyStats.map((stat) => stat.user_count);
					const creditsUsed = dailyStats.map((stat) => stat.credits_used);
					const callCounts = dailyStats.map((stat) => stat.call_count);

					// 用户新增统计图
					const userCtx = document.getElementById("userChart").getContext("2d");
					new Chart(userCtx, {
						type: "bar",
						data: {
							labels: dates,
							datasets: [
								{
									label: "每日新增用户",
									data: userCounts,
									backgroundColor: "rgba(54, 162, 235, 0.5)",
									borderColor: "rgba(54, 162, 235, 1)",
									borderWidth: 1,
								},
							],
						},
						options: {
							scales: {
								y: {
									beginAtZero: true,
									ticks: {
										precision: 0,
									},
								},
							},
							plugins: {
								title: {
									display: true,
									text: "最近7天用户增长趋势",
								},
							},
						},
					});

					// 积分消耗统计图
					const creditsCtx = document.getElementById("creditsChart").getContext("2d");
					new Chart(creditsCtx, {
						type: "line",
						data: {
							labels: dates,
							datasets: [
								{
									label: "每日积分消耗",
									data: creditsUsed,
									backgroundColor: "rgba(75, 192, 192, 0.2)",
									borderColor: "rgba(75, 192, 192, 1)",
									borderWidth: 2,
									tension: 0.1,
								},
							],
						},
						options: {
							scales: {
								y: {
									beginAtZero: true,
								},
							},
							plugins: {
								title: {
									display: true,
									text: "最近7天积分消耗趋势",
								},
							},
						},
					});

					// 调用次数统计图
					const callsCtx = document.getElementById("callsChart").getContext("2d");
					new Chart(callsCtx, {
						type: "bar",
						data: {
							labels: dates,
							datasets: [
								{
									label: "每日调用次数",
									data: callCounts,
									backgroundColor: "rgba(255, 159, 64, 0.5)",
									borderColor: "rgba(255, 159, 64, 1)",
									borderWidth: 1,
								},
							],
						},
						options: {
							scales: {
								y: {
									beginAtZero: true,
									ticks: {
										precision: 0,
									},
								},
							},
							plugins: {
								title: {
									display: true,
									text: "最近7天调用次数趋势",
								},
							},
						},
					});
				} else {
					console.log("没有可用的统计数据");
					document.querySelectorAll('.chart-card').forEach(el => {
						el.innerHTML = "<p style='text-align:center;padding:30px;color:#999;'>暂无数据</p>";
					});
				}
			} catch (error) {
				console.error("图表数据处理错误:", error);
				document.querySelectorAll('.chart-card').forEach(el => {
					el.innerHTML = "<p style='text-align:center;padding:30px;color:#999;'>数据加载失败</p>";
				});
			}
		</script>
	</body>
</html>
