package utils

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/google/uuid"
)

var (
	r2Client *s3.Client
	bucket   string
)

var publicURL string = GetEnvWithDefault("STORAGE_PUBLIC_URL", "https://p1.storage.flowai.cc")

// InitR2Client 初始化 R2 客户端
func InitR2Client() error {
	accountID := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	accessKeyID := os.Getenv("CLOUDFLARE_ACCESS_KEY_ID")
	accessKeySecret := os.Getenv("CLOUDFLARE_ACCESS_KEY_SECRET")
	bucket = os.Getenv("CLOUDFLARE_BUCKET")

	if accountID == "" || accessKeyID == "" || accessKeySecret == "" || bucket == "" {
		return fmt.Errorf("missing required R2 configuration")
	}

	cfg := aws.Config{
		Credentials: credentials.NewStaticCredentialsProvider(accessKeyID, accessKeySecret, ""),
		Region:     "auto",
		BaseEndpoint: aws.String(fmt.Sprintf("https://%s.r2.cloudflarestorage.com", accountID)),
	}

	r2Client = s3.NewFromConfig(cfg)
	return nil
}

// UploadFile 上传文件到 R2
func UploadFile(ctx context.Context, fileData io.Reader, fileName string) (string, error) {
	if r2Client == nil {
		return "", fmt.Errorf("R2 client not initialized")
	}

	// 生成基于日期的路径和UUID的文件名
	ext := filepath.Ext(fileName)
	now := time.Now()
	uuid := uuid.New().String()
	key := fmt.Sprintf("uploads/%d/%02d/%02d/%s%s", 
		now.Year(),
		now.Month(),
		now.Day(),
		uuid,
		ext,
	)

	_, err := r2Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		Body:   fileData,
	})

	if err != nil {
		return "", fmt.Errorf("failed to upload file: %v", err)
	}

	// 返回文件的公共访问URL
	return fmt.Sprintf("%s/%s", publicURL, key), nil
} 
