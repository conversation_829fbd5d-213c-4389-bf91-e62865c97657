package utils

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"io"
)

func RandomSalt(n int) string {
	b := make([]byte, n)
	_, err := io.ReadFull(rand.Reader, b)
	if err != nil {
		panic(err) // Or handle error in a way that's appropriate for your application
	}
	return hex.EncodeToString(b)
}

func PasswordMd5(str string, salt string) string {
	data := []byte(str + salt)
	hash := md5.New()
	hash.Write(data)
	return hex.EncodeToString(hash.Sum(nil))
}
