package utils

import (
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
)

func GetEnvWithDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func CurrentTime() Time {
	t := Time{
		time.Now(),
	}
	return t
}

func GenerateUUID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}
func PushDeerNotify(text string) {
	fmt.Println("PushDeerNotify", text)
	// text should be url encoded
	text = url.QueryEscape(text)
	pushkey := GetEnvWithDefault("PUSH_KEY", "")
	
	notifyUrl := "https://api2.pushdeer.com/message/push?pushkey=" + pushkey + "&text=" + text
	resp, err := http.Get(notifyUrl)
	if err != nil {
		log.Println("PushDeerNotify", err)
	}
	defer resp.Body.Close()
}
