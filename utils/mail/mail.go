package mail

import (
	"bytes"
	"fmt"
	"os"
	"strings"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dm20151123 "github.com/alibabacloud-go/dm-20151123/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/html"
)

var Client *dm20151123.Client

func createClient() (_result *dm20151123.Client, _err error) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")),
		AccessKeySecret: tea.String(os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")),
	}
	// Endpoint 请参考 https://api.aliyun.com/product/Dm
	config.Endpoint = tea.String("dm.aliyuncs.com")
	_result = &dm20151123.Client{}
	_result, _err = dm20151123.NewClient(config)
	return _result, _err
}

func init() {
	client, err := createClient()
	if err != nil {
		panic(err)
	}

	Client = client
}

func getTextFormHtmlBody(htmlBody string) string {
	// 创建HTML解析器
	doc, err := html.Parse(strings.NewReader(htmlBody))
	if err != nil {
		// 如果解析失败，返回原始HTML
		return htmlBody
	}

	var buf bytes.Buffer
	// 递归遍历HTML节点
	var extractText func(node *html.Node, indent int)
	extractText = func(node *html.Node, indent int) {
		if node.Type == html.TextNode {
			// 处理文本节点，去除前后空白
			text := strings.TrimSpace(node.Data)
			if text != "" {
				// 添加适当的缩进
				buf.WriteString(strings.Repeat(" ", indent))
				buf.WriteString(text)
				buf.WriteString("\n")
			}
		} else if node.Type == html.ElementNode {
			// 特殊处理某些HTML元素
			switch node.Data {
			case "br":
				buf.WriteString("\n")
			case "p", "div", "h1", "h2", "h3", "h4", "h5", "h6", "li":
				// 这些元素在前后添加换行
				if buf.Len() > 0 && buf.Bytes()[buf.Len()-1] != '\n' {
					buf.WriteString("\n")
				}
				// 处理子节点
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					extractText(c, indent)
				}
				// 确保元素后有换行
				if buf.Len() > 0 && buf.Bytes()[buf.Len()-1] != '\n' {
					buf.WriteString("\n")
				}
			case "a":
				// 处理链接
				var href string
				for _, attr := range node.Attr {
					if attr.Key == "href" {
						href = attr.Val
						break
					}
				}
				// 提取链接文本
				var linkText bytes.Buffer
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					if c.Type == html.TextNode {
						linkText.WriteString(c.Data)
					}
				}

				if linkText.Len() > 0 {
					buf.WriteString(strings.Repeat(" ", indent))
					buf.WriteString(linkText.String())
					if href != "" && href != linkText.String() {
						buf.WriteString(" (")
						buf.WriteString(href)
						buf.WriteString(")")
					}
					buf.WriteString("\n")
				}
			case "table", "tr", "tbody":
				// 表格元素处理
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					extractText(c, indent)
				}
				buf.WriteString("\n")
			case "th", "td":
				// 表格单元格
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					extractText(c, indent+2)
				}
				buf.WriteString("  ")
			case "ul", "ol":
				// 列表处理
				buf.WriteString("\n")
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					extractText(c, indent+2)
				}
				buf.WriteString("\n")
			default:
				// 处理其他元素
				for c := node.FirstChild; c != nil; c = c.NextSibling {
					extractText(c, indent)
				}
			}
		} else {
			// 处理其他类型的节点
			for c := node.FirstChild; c != nil; c = c.NextSibling {
				extractText(c, indent)
			}
		}
	}

	extractText(doc, 0)

	// 清理文本：删除多余的空行，规范化空白
	lines := strings.Split(buf.String(), "\n")
	var result []string
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return strings.Join(result, "\n")
}

// SendCustomMail 发送自定义邮件
// toAddress: 收信地址
// subject: 邮件主题
// htmlBody: HTML格式的邮件内容
func SendCustomMail(toAddress, subject, htmlBody string) error {
	singleSendMailRequest := &dm20151123.SingleSendMailRequest{
		AccountName:    tea.String(os.Getenv("MAIL_SEND_ACCOUNT")),
		FromAlias:      tea.String("FlowAI"),
		AddressType:    tea.Int32(1), // 1: 发信地址
		ToAddress:      tea.String(toAddress),
		Subject:        tea.String(subject),
		HtmlBody:       tea.String(htmlBody),
		TextBody:       tea.String(getTextFormHtmlBody(htmlBody)),
		ReplyToAddress: tea.Bool(false),
	}

	runtime := &util.RuntimeOptions{}

	_, err := Client.SingleSendMailWithOptions(singleSendMailRequest, runtime)
	if err != nil {
		if sdkErr, ok := err.(*tea.SDKError); ok {
			return fmt.Errorf("发送邮件失败: %s", tea.StringValue(sdkErr.Message))
		}
		return fmt.Errorf("发送邮件失败: %v", err)
	}

	return nil
}
