package mail

import "strings"

const reset_pwd_mail_zh = `<p style="text-align: center;">
    <br/>
</p>
<p style="text-align: center;">
    <img src="https://flowai.cc/main-logo-no-bg.png" style="width: 245px;"/>
</p>
<h1 style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    重置您的密码
</h1>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    我们收到了您重置Flow AI账户密码的请求。请点击下方按钮进行重置：
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <a href="{reset_link}" style="color: rgb(255, 255, 255); font-size: 14px; background-color: rgb(0, 120, 202); padding: 6px 12px; border: 1px solid transparent; border-radius: 4px;">重置密码</a>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <span style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">The FlowAI Team</span>
</p>
<hr/>
<p style="margin-bottom: 0px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    如果您未请求重置密码，请忽略此邮件。若您看不到按钮，可复制以下链接到浏览器访问：
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size:10px"><span style="text-decoration:underline;"><a href="{reset_link}">{reset_link}</a></span></span>
</p>
<p style="text-align: center;">
    <br/>
</p>`

const reset_pwd_mail_en = `<p style="text-align: center;">
    <br/>
</p>
<p style="text-align: center;">
    <img src="https://flowai.cc/main-logo-no-bg.png" style="width: 245px;"/>
</p>
<h1 style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    Reset Your Password
</h1>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    We received a request to reset your Flow AI account password. Please click the button below to proceed:
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <a href="{reset_link}" style="color: rgb(255, 255, 255); font-size: 14px; background-color: rgb(0, 120, 202); padding: 6px 12px; border: 1px solid transparent; border-radius: 4px;">Reset Password</a>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <span style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">The FlowAI Team</span>
</p>
<hr/>
<p style="margin-bottom: 0px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    If you did not request a password reset, please ignore this email. If the button doesn&#39;t work, copy and paste the link below into your browser:
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size:10px"><span style="text-decoration:underline;"><a href="{reset_link}">{reset_link}</a></span></span>
</p>
<p style="text-align: center;">
    <br/>
</p>`

func SendResetPwdMail(lang MailLang, to string, link string) error {
	content := ""
	subject := ""
	switch lang {
	case MAIL_LANG_EN:
		content = reset_pwd_mail_en
		subject = "Reset Password - FlowAI"
	case MAIL_LANG_ZH:
		content = reset_pwd_mail_zh
		subject = "重置密码 - FlowAI"
	}

	content = strings.ReplaceAll(content, "{reset_link}", link)

	return SendCustomMail(to, subject, content)
}
