package mail

import (
	"math/rand"
	"strings"
	"time"
)

// GenerateVerificationCode 生成6位数字验证码
func GenerateVerificationCode() string {
	// 使用新的随机数生成方法，适用于Go 1.20及以上版本
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := ""
	for i := 0; i < 6; i++ {
		code += string(rune('0' + r.Intn(10)))
	}
	return code
}

const reg_mail_zh = `<p style="text-align: center;">
    <br/>
</p>
<p style="text-align: center;">
    <img src="https://flowai.cc/main-logo-no-bg.png" style="width: 245px;"/>
</p>
<h1 style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    请验证您的邮箱
</h1>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    感谢你注册Flow AI. 您的验证码是：
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #0078ca; background-color: #f5f5f5; padding: 10px 20px; border-radius: 4px;">{code}</span>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    请输入此验证码完成邮箱验证。验证码有效期为10分钟。
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    或者，您也可以点击下方按钮直接验证邮箱：
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <a href="{link}" style="color: rgb(255, 255, 255); font-size: 14px; background-color: rgb(0, 120, 202); padding: 6px 12px; border: 1px solid transparent; border-radius: 4px;">点击验证</a>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <span style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">The FlowAI Team</span>
</p>
<hr/>
<p style="margin-bottom: 0px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    如果你看不到按钮，可以复制下面这段链接，通过浏览器访问来验证邮箱：
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size:10px"><span style="text-decoration:underline;"><a href="{link}">{link}</a></span></span>
</p>
<p style="text-align: center;">
    <br/>
</p>`

const reg_mail_en = `<p style="text-align: center;">
    <br/>
</p>
<p style="text-align: center;">
    <img style="width: 245px;" src="https://flowai.cc/main-logo-no-bg.png"/>
</p>
<h1 style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    Please Verify Your Email
</h1>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    Thank you for registering with Flow AI. Your verification code is:
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #0078ca; background-color: #f5f5f5; padding: 10px 20px; border-radius: 4px;">{code}</span>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    Please enter this code to complete your email verification. The code is valid for 10 minutes.
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    Alternatively, you can click the button below to verify your email directly:
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <a style="color: rgb(255, 255, 255); font-size: 14px; background-color: rgb(0, 120, 202); padding: 6px 12px; border: 1px solid transparent; border-radius: 4px;" href="{link}">Click to Verify</a>
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    <span style="color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">The FlowAI Team</span>
</p>
<hr/>
<p style="margin-bottom: 0px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; font-size: small; text-align: -webkit-center; text-wrap: wrap;">
    If you cannot see the button, you can copy the link below and paste it into your browser to verify your email:
</p>
<p style="margin-bottom: 20px; color: rgb(34, 34, 34); font-family: Roboto, sans-serif; text-align: -webkit-center; text-wrap: wrap;">
    <span style="font-size:10px"><span style="text-decoration:underline;"><a href="{link}">{link}</a></span></span>
</p>
<p style="text-align: center;">
    <br/>
</p>`

// SendRegActiveMail 发送带有验证码和链接的注册激活邮件
func SendRegActiveMail(lang MailLang, to string, link string, code string) error {
	content := ""
	subject := ""
	switch lang {
	case MAIL_LANG_EN:
		content = reg_mail_en
		subject = "Verify email and activate account - FlowAI"
	case MAIL_LANG_ZH:
		content = reg_mail_zh
		subject = "验证邮箱并激活账户 - FlowAI"
	}

	content = strings.ReplaceAll(content, "{link}", link)
	content = strings.ReplaceAll(content, "{code}", code)

	return SendCustomMail(to, subject, content)
}
