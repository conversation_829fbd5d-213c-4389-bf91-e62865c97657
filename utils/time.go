package utils

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// NowUnix 返回当前的Unix时间戳（秒）
func NowUnix() int64 {
	return time.Now().Unix()
}

type Time struct {
	time.Time
}

const ctLayout = "2006-01-02 15:04:05"

func (ct *Time) UnmarshalJSON(b []byte) (err error) {
	s := string(b)
	ct.Time, err = time.Parse(ctLayout, s[1:len(s)-1]) // 去除引号
	return
}

func (ct Time) MarshalJSON() ([]byte, error) {
	formatted := ct.Time.Format(ctLayout)
	return json.Marshal(formatted)
}

// Value 实现 driver.Valuer 接口
func (ct Time) Value() (driver.Value, error) {
	if ct.Time.IsZero() {
		return nil, nil
	}
	return ct.Time, nil
}

// Scan 实现 sql.Scanner 接口
func (ct *Time) Scan(value interface{}) error {
	if value == nil {
		ct.Time = time.Time{}
		return nil
	}

	var t time.Time
	switch v := value.(type) {
	case time.Time:
		t = v
	case []byte:
		t = time.Time{}
		if len(v) > 0 {
			var err error
			t, err = time.Parse(ctLayout, string(v))
			if err != nil {
				return err
			}
		}
	default:
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", value))
	}

	ct.Time = t
	return nil
}
